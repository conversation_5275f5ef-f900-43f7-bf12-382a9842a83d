from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from utils.template_helpers import format_text
from typing import Optional, Any, List


def generate_quantity_increments(max_quantity: int, available_stock: int, max_display: int) -> List[int]:
    """
    Intelligent algorithm for dynamic quantity button generation.
    Generates strategic quantity values that scale with the maximum order quantity.

    Args:
        max_quantity: Maximum quantity per order
        available_stock: Available stock
        max_display: Maximum quantities to display before custom option (used for fallback)

    Returns:
        List of quantities to display as buttons
    """
    actual_max = min(max_quantity, available_stock)

    if actual_max <= 0:
        return []

    quantities = []

    # Always include 1 as the first option
    quantities.append(1)

    # Generate strategic intermediate values based on the actual maximum
    if actual_max <= 5:
        # Very small range: show individual numbers
        for i in range(2, actual_max + 1):
            quantities.append(i)

    elif actual_max <= 10:
        # Small range: show key values
        if actual_max >= 5:
            quantities.append(5)
        if actual_max >= 10:
            quantities.append(10)
        elif actual_max > 5:
            quantities.append(actual_max)

    elif actual_max <= 25:
        # Small-medium range: 1, 5, 10, 15/20/25
        quantities.extend([5, 10])
        if actual_max >= 15:
            quantities.append(15)
        if actual_max >= 20:
            quantities.append(20)
        if actual_max == 25:
            quantities.append(25)
        elif actual_max > 20:
            quantities.append(actual_max)

    elif actual_max <= 50:
        # Medium range: 1, 10, 25, 50
        quantities.extend([10, 25])
        if actual_max == 50:
            quantities.append(50)
        else:
            quantities.append(actual_max)

    elif actual_max <= 100:
        # Medium-large range: 1, 10, 25, 50, 100
        quantities.extend([10, 25, 50])
        if actual_max == 100:
            quantities.append(100)
        else:
            quantities.append(actual_max)

    elif actual_max <= 250:
        # Large range: 1, 25, 50, 100, 200/250
        quantities.extend([25, 50, 100])
        if actual_max >= 200:
            quantities.append(200)
        if actual_max == 250:
            quantities.append(250)
        elif actual_max > 200:
            quantities.append(actual_max)

    elif actual_max <= 500:
        # Very large range: 1, 10, 50, 100, 200, 500
        quantities.extend([10, 50, 100, 200])
        if actual_max == 500:
            quantities.append(500)
        else:
            quantities.append(actual_max)

    elif actual_max <= 1000:
        # Extra large range: 1, 25, 100, 250, 500, 1000
        quantities.extend([25, 100, 250, 500])
        if actual_max == 1000:
            quantities.append(1000)
        else:
            quantities.append(actual_max)

    else:
        # Massive range: Use logarithmic distribution
        quantities.extend(_generate_logarithmic_quantities(actual_max))

    # Remove duplicates, sort, and ensure we don't exceed actual_max
    quantities = sorted(list(set(q for q in quantities if q <= actual_max)))

    return quantities


def _generate_logarithmic_quantities(max_quantity: int) -> List[int]:
    """
    Generate logarithmic distribution for very large quantity ranges.

    Args:
        max_quantity: Maximum quantity to generate values for

    Returns:
        List of strategically distributed quantities
    """
    import math

    quantities = []

    # Base values for very large ranges
    base_values = [10, 50, 100, 500, 1000]

    # Add base values that are less than max_quantity
    for value in base_values:
        if value < max_quantity:
            quantities.append(value)

    # Generate logarithmic intermediate values
    if max_quantity > 1000:
        # For ranges > 1000, add strategic points
        log_max = math.log10(max_quantity)

        # Add quarter, half, and three-quarter points
        quarter = int(max_quantity * 0.25)
        half = int(max_quantity * 0.5)
        three_quarter = int(max_quantity * 0.75)

        # Round to nice numbers
        quarter = _round_to_nice_number(quarter)
        half = _round_to_nice_number(half)
        three_quarter = _round_to_nice_number(three_quarter)

        for value in [quarter, half, three_quarter]:
            if value not in quantities and value < max_quantity:
                quantities.append(value)

    # Always include the maximum
    quantities.append(max_quantity)

    return quantities


def _round_to_nice_number(number: int) -> int:
    """
    Round a number to a 'nice' value for button display.

    Args:
        number: Number to round

    Returns:
        Rounded 'nice' number
    """
    if number <= 10:
        return number
    elif number <= 100:
        # Round to nearest 5 or 10
        if number <= 50:
            return round(number / 5) * 5
        else:
            return round(number / 10) * 10
    elif number <= 1000:
        # Round to nearest 25 or 50
        if number <= 500:
            return round(number / 25) * 25
        else:
            return round(number / 50) * 50
    else:
        # Round to nearest 100
        return round(number / 100) * 100


def file_options_keyboard():
    """Keyboard for selecting file options when adding/editing products."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "product_file_link"),
                    callback_data="file_option:link",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "product_file_upload"),
                    callback_data="file_option:upload",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "product_file_text"),
                    callback_data="file_option:text",
                )
            ],
        ]
    )


def back_to_file_options_keyboard():
    """Keyboard for going back to file options."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "product_file_back"),
                    callback_data="back_to_file_options",
                )
            ]
        ]
    )


def back_to_edit_file_options_keyboard():
    """Keyboard for going back to edit file options."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔙 Back to Options", callback_data="back_to_edit_file_options"
                )
            ]
        ]
    )


def product_add_confirmation_keyboard():
    """Keyboard for confirming product addition."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm", callback_data="confirm_add_product"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📝 Edit Details", callback_data="handle_edit_product"
                )
            ],
            [
                InlineKeyboardButton(
                    text="❌ Cancel", callback_data="cancel_add_product"
                )
            ],
        ]
    )


def product_add_success_keyboard():
    """Keyboard shown after successful product addition."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="➕ Add Another Product", callback_data="add_new_product"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Product Management", callback_data="admin_products"
                )
            ],
        ]
    )


def product_edit_keyboard(product_id):
    """Keyboard for editing product fields."""
    # Note: product_id parameter is kept for future use in callback data
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(text="✏️ Edit Name", callback_data="edit_name"),
                InlineKeyboardButton(
                    text="✏️ Edit Description", callback_data="edit_description"
                ),
            ],
            [
                InlineKeyboardButton(text="✏️ Edit Price", callback_data="edit_price"),
                InlineKeyboardButton(
                    text="✏️ Edit File Link", callback_data="edit_file_link"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="✏️ Edit Image URL", callback_data="edit_image_url"
                ),
                InlineKeyboardButton(
                    text="✏️ Edit Category", callback_data="edit_category"
                ),
            ],
            [
                InlineKeyboardButton(text="✏️ Edit Tags", callback_data="edit_tags"),
                InlineKeyboardButton(
                    text="✏️ Edit Quantity", callback_data="edit_quantity"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="✏️ Edit Availability", callback_data="edit_availability"
                ),
                InlineKeyboardButton(
                    text="🔍 Preview Product", callback_data="preview_product"
                ),
            ],
            [
                InlineKeyboardButton(text="✅ Done", callback_data="done_editing"),
                InlineKeyboardButton(
                    text="🔙 Back to Categories", callback_data="edit_product_menu"
                ),
            ],
        ]
    )


def product_preview_keyboard(product_id):
    """Keyboard for product preview."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔙 Back to Edit", callback_data=f"edit_product:{product_id}"
                ),
                InlineKeyboardButton(
                    text="✅ Looks Good", callback_data="confirm_add_product"
                ),
            ],
        ]
    )


def availability_keyboard():
    """Keyboard for selecting product availability."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Available", callback_data="set_availability:available"
                )
            ],
            [
                InlineKeyboardButton(
                    text="❌ Unavailable/Coming Soon",
                    callback_data="set_availability:unavailable",
                )
            ],
        ]
    )


def product_type_keyboard():
    """Keyboard for selecting product type during creation."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📄 Regular Product", callback_data="product_type:regular"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📋 Line-Based Product", callback_data="product_type:line_based"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔐 Exclusive Single-Use", callback_data="product_type:exclusive"
                )
            ],
        ]
    )


def line_product_file_keyboard():
    """Keyboard for line-based product file options."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📁 Upload Inventory File", callback_data="line_file:upload"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔗 Provide File Link", callback_data="line_file:link"
                )
            ],
            [
                InlineKeyboardButton(
                    text="⬅️ Back", callback_data="back_to_product_type"
                )
            ],
        ]
    )


def exclusive_product_file_keyboard():
    """Keyboard for exclusive single-use product file options."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📁 Upload Exclusive File", callback_data="exclusive_file:upload"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔗 Provide File Link", callback_data="exclusive_file:link"
                )
            ],
            [
                InlineKeyboardButton(
                    text="⬅️ Back", callback_data="back_to_product_type"
                )
            ],
        ]
    )


def exclusive_product_confirmation_keyboard(product_id: Any):
    """Keyboard for confirming exclusive product purchase."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="⭐ Buy Exclusive Product",
                    callback_data=f"buy_product:{product_id}"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🛒 Add to Cart",
                    callback_data=f"add_to_cart:{product_id}"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Product",
                    callback_data=f"view_product:{product_id}"
                )
            ],
        ]
    )


def quantity_selection_keyboard(
    max_quantity: int,
    available_stock: int,
    product_id: Optional[Any] = None,
    buttons_per_row: int = 5,
    max_display_buttons: int = 10,
    callback_prefix: str = "select_quantity"
):
    """
    Enhanced keyboard for selecting quantity of line-based products with dynamic range support.

    Args:
        max_quantity: Maximum quantity per order for the product
        available_stock: Currently available stock
        product_id: Product ID for callback data (optional)
        buttons_per_row: Number of buttons per row (default: 5)
        max_display_buttons: Maximum buttons to display before showing custom option (default: 10)
        callback_prefix: Prefix for callback data (default: "select_quantity")

    Returns:
        InlineKeyboardMarkup with dynamic quantity selection
    """
    # Determine the actual maximum selectable quantity
    actual_max = min(max_quantity, available_stock)

    # Validate inputs
    if actual_max <= 0:
        # Return empty keyboard with just back button if no stock
        return InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(
                text="🔙 Back",
                callback_data=f"view_product:{product_id}" if product_id else "back_to_product"
            )]
        ])

    # Determine how many buttons to display
    # Apply safety limit to prevent "reply markup is too long" errors
    max_display_buttons = min(max_display_buttons, 20)  # Hard limit of 20 buttons
    display_max = min(actual_max, max_display_buttons)

    buttons = []
    current_row = []

    # Generate quantity list with increments of 5 (plus 1)
    quantities_to_show = generate_quantity_increments(max_quantity, available_stock, display_max)

    # Create buttons for the selected quantities
    for qty in quantities_to_show:
        # Create callback data with product_id if provided
        if product_id:
            callback_data = f"line_quantity:{product_id}:{qty}"
        else:
            callback_data = f"{callback_prefix}:{qty}"

        button = InlineKeyboardButton(
            text=str(qty),
            callback_data=callback_data
        )
        current_row.append(button)

        # Add row when we have enough buttons or reached the end
        if len(current_row) == buttons_per_row or qty == quantities_to_show[-1]:
            buttons.append(current_row)
            current_row = []

    # Add custom quantity option if needed
    if actual_max > display_max:
        if product_id:
            custom_callback = f"line_custom_quantity:{product_id}"
        else:
            custom_callback = f"{callback_prefix}:custom"

        buttons.append([
            InlineKeyboardButton(
                text=f"📝 Custom (Max: {actual_max})",
                callback_data=custom_callback
            )
        ])

    # Add navigation buttons
    back_callback = f"view_product:{product_id}" if product_id else "back_to_product"
    buttons.append([
        InlineKeyboardButton(text="🔙 Back", callback_data=back_callback)
    ])

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def enhanced_line_quantity_keyboard(
    product_id: Any,
    max_quantity: int,
    available_stock: int,
    line_price: float = 0.0,
    show_price_preview: bool = True,
    buttons_per_row: int = 5,
    max_display_buttons: int = 10
):
    """
    Enhanced quantity selection keyboard with price preview and line product theming.
    Integrates with the consolidated LineProductManager system.

    Args:
        product_id: Product ID for callback data
        max_quantity: Maximum quantity per order
        available_stock: Available stock count
        line_price: Price per line item for preview calculations
        show_price_preview: Whether to show price in button text
        buttons_per_row: Number of buttons per row
        max_display_buttons: Maximum buttons before custom option

    Returns:
        InlineKeyboardMarkup with enhanced quantity selection
    """
    # Import theming for consistent emoji usage
    try:
        from utils.line_product_manager import LineProductTheme
        price_emoji = LineProductTheme.EMOJIS['price']
        custom_emoji = LineProductTheme.EMOJIS['format']
    except ImportError:
        # Fallback emojis if theming not available
        price_emoji = "💰"
        custom_emoji = "📝"

    # Determine the actual maximum selectable quantity
    actual_max = min(max_quantity, available_stock)

    # Apply safety limit to prevent "reply markup is too long" errors
    max_display_buttons = min(max_display_buttons, 20)  # Hard limit of 20 buttons

    # Handle edge cases
    if actual_max <= 0:
        return InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(
                text="❌ Out of Stock",
                callback_data=f"view_product:{product_id}"
            )],
            [InlineKeyboardButton(
                text="🔙 Back",
                callback_data=f"view_product:{product_id}"
            )]
        ])

    # Determine display range
    display_max = min(actual_max, max_display_buttons)

    buttons = []
    current_row = []

    # Generate quantity list with increments of 5 (plus 1) and optional price preview
    quantities_to_show = generate_quantity_increments(max_quantity, available_stock, display_max)

    # Create buttons for the selected quantities with optional price preview
    for qty in quantities_to_show:
        if show_price_preview and line_price > 0:
            total_price = line_price * qty
            button_text = f"{qty} ({price_emoji}${total_price:.2f})"
        else:
            button_text = str(qty)

        button = InlineKeyboardButton(
            text=button_text,
            callback_data=f"line_quantity:{product_id}:{qty}"
        )
        current_row.append(button)

        # Add row when we have enough buttons or reached the end
        if len(current_row) == buttons_per_row or qty == quantities_to_show[-1]:
            buttons.append(current_row)
            current_row = []

    # Add custom quantity option if needed
    if actual_max > display_max:
        buttons.append([
            InlineKeyboardButton(
                text=f"{custom_emoji} Custom (Max: {actual_max})",
                callback_data=f"line_custom_quantity:{product_id}"
            )
        ])

    # Add quick action buttons for common quantities
    if actual_max >= 5 and display_max < actual_max:
        quick_actions = []

        # Add "Max" button if max is reasonable
        if actual_max <= 100:
            quick_actions.append(
                InlineKeyboardButton(
                    text=f"🔝 Max ({actual_max})",
                    callback_data=f"line_quantity:{product_id}:{actual_max}"
                )
            )

        # Add "Half" button if applicable
        if actual_max >= 10:
            half_qty = actual_max // 2
            quick_actions.append(
                InlineKeyboardButton(
                    text=f"⚡ Half ({half_qty})",
                    callback_data=f"line_quantity:{product_id}:{half_qty}"
                )
            )

        if quick_actions:
            buttons.append(quick_actions)

    # Add navigation buttons
    buttons.append([
        InlineKeyboardButton(text="🔙 Back", callback_data=f"view_product:{product_id}")
    ])

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def create_line_quantity_keyboard_with_validation(
    product_id: Any,
    product_data: dict,
    context: str = "add_to_cart"
):
    """
    Create quantity keyboard with integrated validation and theming.
    This function integrates with the consolidated LineProductManager for validation.

    Args:
        product_id: Product ID
        product_data: Product dictionary with line-based data
        context: Context for the keyboard (add_to_cart, buy_now, etc.) - reserved for future use

    Returns:
        InlineKeyboardMarkup with validated quantity options
    """
    # Note: context parameter is reserved for future context-specific customizations
    try:
        # Import the consolidated manager for validation
        from utils.line_product_manager import line_product_manager

        # Use the consolidated manager's keyboard creation
        max_quantity = product_data.get("max_quantity_per_order", 1)
        available_stock = product_data.get("available_lines", 0)

        return line_product_manager.create_quantity_selection_keyboard(
            product_id, max_quantity, available_stock
        )

    except ImportError:
        # Fallback to enhanced keyboard if manager not available
        max_quantity = product_data.get("max_quantity_per_order", 1)
        available_stock = product_data.get("available_lines", 0)
        line_price = product_data.get("line_price", product_data.get("price", 0))

        return enhanced_line_quantity_keyboard(
            product_id=product_id,
            max_quantity=max_quantity,
            available_stock=available_stock,
            line_price=line_price,
            show_price_preview=True
        )


def smart_quantity_keyboard(
    max_quantity: int,
    available_stock: int,
    product_id: Optional[Any] = None,
    line_price: float = 0.0,
    use_enhanced_features: bool = True
):
    """
    Smart quantity keyboard that automatically chooses the best layout based on constraints.

    Args:
        max_quantity: Maximum quantity per order
        available_stock: Available stock
        product_id: Product ID (optional)
        line_price: Price per line for preview
        use_enhanced_features: Whether to use enhanced features like price preview

    Returns:
        InlineKeyboardMarkup optimized for the given constraints
    """
    actual_max = min(max_quantity, available_stock)

    # Choose layout based on quantity range (optimized for dynamic button generation)
    # Limit max_display to prevent "reply markup is too long" errors
    if actual_max <= 10:
        # Small range: 2-4 buttons, show all in one row
        buttons_per_row = 4
        max_display = actual_max
    elif actual_max <= 50:
        # Medium range: 3-4 buttons, arrange in rows of 3-4
        buttons_per_row = 3
        max_display = min(actual_max, 20)  # Limit to 20 buttons max
    elif actual_max <= 500:
        # Large range: 4-6 buttons, arrange in rows of 3
        buttons_per_row = 3
        max_display = min(actual_max, 15)  # Limit to 15 buttons max
    else:
        # Very large range: 5-7 buttons, arrange in rows of 3
        buttons_per_row = 3
        max_display = min(actual_max, 10)  # Limit to 10 buttons max for very large ranges

    if use_enhanced_features and product_id and line_price > 0:
        return enhanced_line_quantity_keyboard(
            product_id=product_id,
            max_quantity=max_quantity,
            available_stock=available_stock,
            line_price=line_price,
            show_price_preview=True,
            buttons_per_row=buttons_per_row,
            max_display_buttons=max_display
        )
    else:
        return quantity_selection_keyboard(
            max_quantity=max_quantity,
            available_stock=available_stock,
            product_id=product_id,
            buttons_per_row=buttons_per_row,
            max_display_buttons=max_display
        )


def line_product_confirmation_keyboard():
    """Keyboard for confirming line-based product purchase."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm Purchase", callback_data="confirm_line_purchase"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔄 Change Quantity", callback_data="change_quantity"
                )
            ],
            [
                InlineKeyboardButton(
                    text="⬅️ Back to Product", callback_data="back_to_product"
                )
            ],
        ]
    )


def done_editing_keyboard():
    """Keyboard for when editing is done."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔙 Back to Products", callback_data="admin_products"
                )
            ]
        ]
    )


def confirm_delete_product_keyboard(product_id):
    """Keyboard for confirming product deletion."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="⚠️ Yes, Delete This Product",
                    callback_data=f"execute_delete_product:{product_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text="❌ No, Cancel", callback_data="delete_product_menu"
                )
            ],
        ]
    )


def back_to_product_management_keyboard():
    """Keyboard for returning to product management."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔙 Back to Product Management", callback_data="admin_products"
                )
            ]
        ]
    )


def product_deleted_keyboard():
    """Keyboard shown after successful product deletion."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔙 Back to Product Management", callback_data="admin_products"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🗑️ Delete Another Product", callback_data="delete_product_menu"
                )
            ],
        ]
    )


def product_edit_field_success_keyboard(product_id):
    """Keyboard shown after successfully editing a product field."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✏️ Edit Another Field",
                    callback_data=f"edit_product:{product_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Product Management", callback_data="admin_products"
                )
            ],
        ]
    )


def product_edit_options_keyboard():
    """Extended keyboard for product editing options."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [InlineKeyboardButton(text="✏️ Edit Name", callback_data="edit_name")],
            [
                InlineKeyboardButton(
                    text="✏️ Edit Description", callback_data="edit_description"
                )
            ],
            [InlineKeyboardButton(text="✏️ Edit Price", callback_data="edit_price")],
            [
                InlineKeyboardButton(
                    text="✏️ Edit File Link", callback_data="edit_file_link"
                )
            ],
            [
                InlineKeyboardButton(
                    text="✏️ Edit Image URL", callback_data="edit_image_url"
                )
            ],
            [InlineKeyboardButton(text="✏️ Edit Tags", callback_data="edit_tags")],
            [
                InlineKeyboardButton(
                    text="🏷️ Add Tags", callback_data="add_product_tags"
                ),
                InlineKeyboardButton(
                    text="🔢 Set Quantity", callback_data="set_product_quantity"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="� Set Availability", callback_data="add_availability_option"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔍 Preview", callback_data="preview_product"
                ),
                InlineKeyboardButton(
                    text="✅ Done", callback_data="confirm_add_product"
                ),
            ],
        ]
    )


def category_selection_keyboard(categories, current_category_id=None):
    """Keyboard for selecting a product category."""
    inline_keyboard = []

    # Add buttons for each category
    for category in categories:
        category_id = category.get("id") or category.get("_id")
        category_name = category.get("name") or "Unnamed Category"

        # Mark current category if applicable
        if category_id == current_category_id:
            category_name = f"✓ {category_name}"

        inline_keyboard.append(
            [
                InlineKeyboardButton(
                    text=category_name, callback_data=f"select_category:{category_id}"
                )
            ]
        )

    # Add skip option
    inline_keyboard.append(
        [
            InlineKeyboardButton(
                text="Skip (No Category)", callback_data="select_category:none"
            )
        ]
    )

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def product_deletion_confirmation_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for product deletion confirmation."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="Yes, Delete", callback_data="confirm_delete"
                ),
                InlineKeyboardButton(text="Cancel", callback_data="cancel_delete"),
            ]
        ]
    )
