# Standard library imports
import random
import logging
import os
from datetime import datetime
from utils.image_handler import PRODUCT_IMAGES_FOLDER

# Third-party imports
from aiogram import Bo<PERSON>, Dispatcher, Router, types, F
from aiogram.fsm.context import FSMContext
from aiogram.filters.state import StateFilter
from aiogram.filters import Command
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton, FSInputFile
from aiogram.exceptions import TelegramBadRequest
import asyncio
from utils.local_file_handling import (
    extract_image_from_object,
)
from utils.line_product_manager import LineProductTheme

try:
    from bson import ObjectId
except ImportError:
    ObjectId = None  # Handle environments without bson

# Local/application imports
from database.operations import (
    get_products,
    get_product,
    get_user_balance,
    get_or_create_cart,
    update_cart,
    clear_cart,
    add_transaction,
    update_user_balance,
    get_all_categories,  # Moved import to top
    get_products_without_category,  # Moved import to top
    get_products_by_category,  # Moved import to top
    get_category_by_id,  # Moved import to top
    # get_or_create_cart already imported above
)
from states.states import OrderStates  # PurchaseStates removed as it's not used
from keyboards.user_kb import (
    cart_keyboard,  # Used in view_cart_message
)
from keyboards.shop_kb import (
    product_detail_keyboard,
)  # Keep this, used in select_product
from config import ADMIN_ID
from utils.logger import log_purchase  # Keep this
from utils.telegram_helpers import (
    safe_edit_message,
)  # Keep these
from utils.privileged_operations import should_bypass_balance_check  # Keep this
from utils.button_layout import should_use_single_row  # Keep this
from utils.pagination import (
    paginate_items,
)  # For product pagination
from utils.state_helpers import safe_update_data, clear_state_data

# Cart manager imports removed due to deadlock - using direct implementation

# Create single router instance for all product-related handlers
router = Router()
router.name = "products_router"

# Use standard Python logging
logger = logging.getLogger(__name__)

# --- Helper Functions ---


def _get_product_id_from_string(product_id_str):
    """Safely converts a string Product ID to int or ObjectId if possible."""
    if ObjectId and ObjectId.is_valid(product_id_str):
        return ObjectId(product_id_str)
    try:
        return int(product_id_str)
    except ValueError:
        return product_id_str  # Fallback to string if not valid ObjectId or int


# --- Category & Product Browsing ---


@router.callback_query(F.data == "browse_products")
async def browse_products(callback_query: types.CallbackQuery):
    """Display product categories or a message if none exist."""
    try:
        await callback_query.answer()

        categories = get_all_categories() or []
        # Sort categories by creation time (assuming 'created_at' exists), newest first
        # Add robust check for presence and type of 'created_at'
        categories = sorted(
            [
                cat for cat in categories if isinstance(cat, dict)
            ],  # Ensure items are dicts
            key=lambda x: (
                x.get("created_at", datetime.min)
                if isinstance(x.get("created_at"), datetime)
                else datetime.min
            ),
            reverse=True,
        )
        logger.info(f"Found and sorted {len(categories)} categories.")

        products_without_category = get_products_without_category()
        has_uncategorized = bool(products_without_category)

        inline_keyboard = []
        category_buttons = []
        button_style = "\ud83d\udcc2"  # Folder emoji

        for category in categories:
            category_id = category.get("_id") or category.get("id")
            category_name = str(
                category.get("name", "Unnamed Category")
            )  # Ensure string
            if not category_id:
                logger.warning(f"Category '{category_name}' missing ID, skipping.")
                continue

            # Get products count for this category
            category_products = get_products_by_category(category_id)
            product_count = len(category_products) if category_products else 0

            button_text = f"{button_style} {category_name} ({product_count})"
            button = InlineKeyboardButton(
                text=button_text,
                callback_data=f"browse_category:{category_id}",
            )
            # Use button layout helper - check full button text for proper layout
            if should_use_single_row(button_text):
                inline_keyboard.append([button])
            else:
                category_buttons.append(button)
                if len(category_buttons) == 2:
                    inline_keyboard.append(category_buttons)
                    category_buttons = []

        if category_buttons:  # Add remaining buttons if odd number
            inline_keyboard.append(category_buttons)

        if has_uncategorized:
            uncategorized_count = len(products_without_category)
            inline_keyboard.append(
                [
                    InlineKeyboardButton(
                        text=f"{button_style} No Category ({uncategorized_count})",
                        callback_data="browse_category:no_category",
                    )
                ]
            )

        # Add "View All" and "Back" buttons
        total_products = sum(
            len(get_products_by_category(cat.get("_id") or cat.get("id")))
            for cat in categories
            if cat.get("_id") or cat.get("id")
        )
        if has_uncategorized:
            total_products += len(products_without_category)

        inline_keyboard.append(
            [
                InlineKeyboardButton(
                    text=f"\ud83d\udd0d View All Products ({total_products})",
                    callback_data="browse_all_products",
                )
            ]
        )
        inline_keyboard.append(
            [
                InlineKeyboardButton(
                    text="\ud83d\udd19 Back to Main Menu",
                    callback_data="return_to_main",
                )
            ]
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=inline_keyboard)

        # Clean catalog text with professional styling
        catalog_text = (
            "<b>• PRODUCT CATEGORIES •</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>BROWSE COLLECTION</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"<b>Categories Available:</b> <code>{len(categories)}</code>\n"
            f"<b>Total Products:</b> <code>{total_products}</code>\n"
            f"<b>Browse Options:</b> <i>By category or view all</i>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
            "<i>Select a category below to explore our products</i>\n"
            "<i>Or view our complete collection</i>"
        )

        if not categories and not has_uncategorized:
            catalog_text = (
                "<b>• PRODUCT CATEGORIES •</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>GETTING STARTED</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
                "<i>No product categories found yet</i>\n"
                "<i>You can still browse all available products</i>\n"
                "<i>New categories are added regularly</i>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>"
            )

        await safe_edit_message(
            callback_query.message,
            catalog_text,
            reply_markup=keyboard,
            parse_mode="HTML",
        )

    except Exception as e:
        logger.exception("Error displaying categories:", exc_info=e)
        fallback_keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="\ud83d\udd04 Try Again", callback_data="browse_products"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="\ud83c\udfe0 Main Menu", callback_data="return_to_main"
                    )
                ],
            ]
        )
        try:
            await safe_edit_message(
                callback_query.message,
                "❌ <b>• SYSTEM ERROR •</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>LOADING ISSUE</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
                "<i>An error occurred while loading categories</i>\n"
                "<i>Please try again or contact support if the issue persists</i>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>",
                reply_markup=fallback_keyboard,
                parse_mode="HTML",
            )
        except Exception as fallback_e:
            logger.error(
                f"Failed to send category error fallback message: {fallback_e}"
            )


@router.callback_query(F.data.startswith("browse_category:"))
async def browse_category_products(callback_query: types.CallbackQuery):
    """Browse products in a specific category with enhanced UI presentation."""
    try:
        # Check if this is a paginated request
        data_parts = callback_query.data.split(":")
        category_id = data_parts[1]  # Get category ID
        page = 1  # Default page

        # Handle pagination (format: browse_category:category_id:page_num)
        if len(data_parts) > 2 and data_parts[2].isdigit():
            page = int(data_parts[2])

        await callback_query.answer()

        # Special handling for "no_category"
        if category_id == "no_category":
            # Create a dummy category object for uncategorized products
            category = {
                "name": "Uncategorized Products",
                "description": "Premium products without assigned categories",
            }
        else:
            # Get category information for regular categories
            category = get_category_by_id(category_id)
            if not category:
                logger.error(f"Category not found: {category_id}")
                await handle_category_error(callback_query, "Category not found")
                return

        # Get products for this category
        products = get_products_by_category(category_id)

        # Get category image if available
        category_image_source = extract_image_from_object(
            category, object_type="category"
        )
        logger.debug(f"Found category image: {category_image_source}")

        # Additional check to ensure we have a valid image path
        if category_image_source and not category_image_source.startswith(
            ("http://", "https://", "file_id:")
        ):
            # Check if the file exists
            if not os.path.isfile(category_image_source):
                # Try to find the file in the category_images directory
                base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                category_images_dir = os.path.join(
                    base_path, "uploads", "category_images"
                )

                # Try with just the filename
                filename = os.path.basename(category_image_source)
                alt_path = os.path.join(category_images_dir, filename)

                if os.path.isfile(alt_path):
                    category_image_source = alt_path
                    logger.debug(
                        f"Found category image at alternative path: {alt_path}"
                    )
                else:
                    # Try to find any file in the directory that contains the category ID
                    category_id_str = str(category_id)
                    for file in os.listdir(category_images_dir):
                        if category_id_str in file and os.path.isfile(
                            os.path.join(category_images_dir, file)
                        ):
                            category_image_source = os.path.join(
                                category_images_dir, file
                            )
                            logger.debug(
                                f"Found category image by ID match: {category_image_source}"
                            )
                            break

        # Paginate products
        items_per_page = 5
        paginated_products, total_pages = paginate_items(products, page, items_per_page)

        if not paginated_products:
            # No products in this category - enhanced empty state
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Categories",
                            callback_data="browse_products",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🛍️ Browse All Products",
                            callback_data="browse_all_products",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🏠 Main Menu", callback_data="return_to_main"
                        )
                    ],
                ]
            )

            category_name = category.get("name", "Unknown Category")
            category_description = category.get("description", "")

            # Clean empty category message with refined styling
            empty_catalog_text = (
                f"<b>• {category_name.upper()} •</b>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>CATEGORY STATUS</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
            )

            if category_description:
                empty_catalog_text += f"<i>{category_description}</i>\n\n"

            empty_catalog_text += (
                f"<b>Products Available:</b> <code>0</code>\n"
                f"<b>Status:</b> <i>Coming Soon</i>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"<i>New products are added regularly</i>\n"
                f"<i>Check back soon or explore our other categories</i>\n"
                f"<i>Browse our complete collection below</i>"
            )

            # Send message with category image if available
            chat_id = callback_query.message.chat.id
            message_id = callback_query.message.message_id
            is_photo = callback_query.message.photo is not None

            await send_catalog_message(
                callback_query.bot,
                chat_id,
                message_id,
                empty_catalog_text,
                keyboard,
                category_image_source,  # Use category image even for empty categories
                is_photo=is_photo,
                context="category",  # Specify this is a category context
            )
            return

        # Build clean product list display with refined buttons
        inline_keyboard = []

        # Add clean product buttons with professional styling
        for i, product in enumerate(paginated_products, 1):
            product_id = product.get("_id") or product.get("id")
            product_name = str(product.get("name", "Premium Product"))
            price = product.get("price", 0) or 0

            if not product_id:
                continue

            # Clean button text with price and numbering
            button_text = f"{i}. {product_name} • ${price:.2f}"

            # Truncate button text if too long to prevent layout issues
            if len(button_text) > 45:
                truncated_name = product_name[:32] + "..."
                button_text = f"{i}. {truncated_name} • ${price:.2f}"

            button = InlineKeyboardButton(
                text=button_text, callback_data=f"select_product:{product_id}"
            )

            # Single-column layout for better readability
            inline_keyboard.append([button])

        # Clean pagination controls with refined styling
        if total_pages > 1:
            pagination_buttons = []

            # Previous page button with clean styling
            if page > 1:
                prev_callback = f"browse_category:{category_id}:{page - 1}"
                pagination_buttons.append(
                    InlineKeyboardButton(
                        text="⬅️ Previous", callback_data=prev_callback
                    )
                )

            # Clean page indicator
            pagination_buttons.append(
                InlineKeyboardButton(
                    text=f"Page {page}/{total_pages}", callback_data="noop"
                )
            )

            # Next page button with clean styling
            if page < total_pages:
                next_callback = f"browse_category:{category_id}:{page + 1}"
                pagination_buttons.append(
                    InlineKeyboardButton(text="Next ➡️", callback_data=next_callback)
                )

            inline_keyboard.append(pagination_buttons)

        # Clean navigation buttons with organized layout
        navigation_row_1 = [
            InlineKeyboardButton(
                text="🔙 Back to Categories", callback_data="browse_products"
            ),
            InlineKeyboardButton(
                text="All Products", callback_data="browse_all_products"
            )
        ]

        navigation_row_2 = [
            InlineKeyboardButton(
                text="🛒 View Cart", callback_data="view_cart"
            ),
            InlineKeyboardButton(
                text="🏠 Main Menu", callback_data="return_to_main"
            )
        ]

        inline_keyboard.append(navigation_row_1)
        inline_keyboard.append(navigation_row_2)

        keyboard = InlineKeyboardMarkup(inline_keyboard=inline_keyboard)

        # Build refined catalog text with clean design
        category_name = category.get("name", "Category")
        category_description = category.get("description", "")
        product_count = len(products) if products else 0

        # Clean header with professional typography
        catalog_text = f"<b>• {category_name.upper()} •</b>\n\n"
        catalog_text += f"<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
        catalog_text += f"<b>PRODUCT CATALOG</b>\n"
        catalog_text += f"<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"

        # Add category description if available
        if category_description:
            catalog_text += f"<i>{category_description}</i>\n\n"

        # Add category statistics with minimal decoration
        catalog_text += f"<b>Total Products:</b> <code>{product_count}</code>\n"
        catalog_text += f"<b>Showing:</b> <code>{len(paginated_products)} items</code>\n"
        if total_pages > 1:
            catalog_text += f"<b>Page:</b> <code>{page} of {total_pages}</code>\n"
        catalog_text += f"\n<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"

        # Clean product listing with structured hierarchy
        for i, product in enumerate(paginated_products, 1):
            name = product.get("name", "Premium Product")
            price = product.get("price", 0) or 0
            description = (
                product.get("description", "Exclusive digital product") or "Exclusive digital product"
            )

            # Get first line and limit to 65 characters for better readability
            first_line = description.split("\n", 1)[0]
            desc_short = first_line[:65] + ("..." if len(first_line) > 65 else "")

            # Clean product display with structured formatting
            catalog_text += f"<b>{i}. {name}</b>\n"
            catalog_text += f"   <i>{desc_short}</i>\n"
            catalog_text += f"   <b>💎 Price:</b> <code>${price:.2f}</code>\n"

            # Add subtle separator between products (except last one)
            if i < len(paginated_products):
                catalog_text += f"   ┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\n"

        # Clean footer with clear call-to-action
        catalog_text += f"\n<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
        catalog_text += f"<i>Select any product below to view details and purchase options</i>"

        # Send message with category image
        chat_id = callback_query.message.chat.id
        message_id = callback_query.message.message_id
        is_photo = callback_query.message.photo is not None

        # Make sure we're using the category image for category listings
        await send_catalog_message(
            callback_query.bot,
            chat_id,
            message_id,
            catalog_text,
            keyboard,
            category_image_source,  # Use category image for category listing
            is_photo=is_photo,
            context="category",  # Explicitly specify this is a category context
        )

    except Exception as e:
        logger.exception(f"Error browsing category: {e}")
        await handle_category_error(callback_query, "Failed to load category products")


async def handle_empty_category(
    callback_query: types.CallbackQuery,
    category_name,
    category_description,
    category_image_source,
):
    """Handles display when a category has no products."""
    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔙 Back to Categories", callback_data="browse_products"
                )
            ],
            [InlineKeyboardButton(text="🏠 Main Menu", callback_data="return_to_main")],
        ]
    )
    empty_category_text = (
        f"📂 <b>\u2022 {category_name} \u2022</b> 📂\n\n"
        f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
        + (f"<i>{category_description}</i>\n\n" if category_description else "")
        + f"<i>No products available in this category yet.</i>\n"
        f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        f"<i>Please check back later or explore other categories!</i> ✨"
    )

    is_photo = callback_query.message.photo is not None
    await send_catalog_message(
        callback_query.bot,
        callback_query.message.chat.id,
        callback_query.message.message_id,
        empty_category_text,
        keyboard,
        category_image_source,
        is_photo=is_photo,
        context="category",  # Specify that this is a category context
    )


def create_category_catalog_text_with_pagination(
    category_name, category_description, products, page, total_pages
):
    """Creates the formatted text listing products in a category with pagination."""
    # Elegant header with decorative elements
    catalog_text = f"✧༺ <b>{category_name}</b> ༻✧\n\n"
    catalog_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n"
    if category_description:
        catalog_text += f"<i>{category_description}</i>\n"
    catalog_text += "<i>Select a product to view details:</i>\n"
    catalog_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"

    # Enhanced product styling with better visual hierarchy
    for i, product in enumerate(products):
        name = product.get("name", "Unknown Product")
        price = product.get("price") or 0  # Ensure price is not None

        # Add subtle separator between products (except the first one)
        if i > 0:
            catalog_text += "┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\n"

        # Product name with more elegant formatting and price
        catalog_text += f"<b>{i+1}.</b> <b>{name}</b>\n"
        catalog_text += f"<code>⟡ ${price:.2f}</code>\n"

    # Add divider after the last item if there are any products
    if products:
        catalog_text += "┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\n"

    # Footer with pagination info
    catalog_text += "\n"
    if total_pages > 1:
        catalog_text += (
            f"<b>\u2022\u2022\u2022 Page {page}/{total_pages} \u2022\u2022\u2022</b>\n"
        )
    catalog_text += "<i>✨ Tap a product name in the buttons below</i>"

    return catalog_text


async def find_first_valid_product_image(products):
    """Finds the first valid image source from a list of products."""
    if not products:
        return None
    for product in products:
        image_source = extract_image_from_object(product, object_type="product")
        if image_source:
            return image_source
    return None


async def send_catalog_message(
    bot: Bot,
    chat_id: int,
    message_id: int,
    text: str,
    keyboard: InlineKeyboardMarkup,
    image_source: str | dict | None,
    is_photo: bool,
    context: str = "generic",
):
    """
    Sends or edits a catalog message with optional image.
    Handles both photo and text messages appropriately.
    Implements image unloading - only shows images in appropriate contexts:
    - Category images only appear in category view
    - Product images only shown if the product has an image
    - All other contexts use text-only display

    Args:
        bot: Bot instance
        chat_id: Chat ID where the message should be sent
        message_id: Message ID to edit (if editing an existing message)
        text: Text content for the message
        keyboard: InlineKeyboardMarkup for the message
        image_source: Image URL, file_id, or path
        is_photo: Whether the current message is a photo message
        context: Context identifier ("product", "category", etc.)
    """
    logger.info(f"Sending catalog message in context: {context}")

    # Implement image unloading - only process image if in appropriate context
    photo_arg = None

    # First check if we should even try to load an image based on context
    should_process_image = False

    if context == "product":
        # For products, only show image if product has one
        should_process_image = image_source is not None
        logger.debug(f"Product context: will process image = {should_process_image}")
    elif context == "category":
        # For categories, always try to show the category image
        should_process_image = image_source is not None
        logger.debug(f"Category context: will process image = {should_process_image}")
    else:
        # For all other contexts (all_products, etc.), never show images
        should_process_image = False
        logger.debug(f"Context '{context}': will not process images")

    # Process the image source to get a file_id or URL only if we should show an image
    if should_process_image and image_source is not None:
        if isinstance(image_source, str):
            logger.info(
                f"Processing image source for {context}: {image_source[:50]}..."
            )

            if image_source.startswith(("http://", "https://")):
                # Use URL directly
                photo_arg = image_source
                logger.debug(
                    f"Using direct URL for {context} image: {photo_arg[:50]}..."
                )
            elif image_source.startswith("file_id:"):
                # Extract file_id from the string
                photo_arg = image_source.replace("file_id:", "")
                logger.debug(f"Using file_id for {context} image: {photo_arg}")
            elif image_source.startswith("uploads/"):
                # Handle relative paths inside uploads folder
                base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                full_path = os.path.join(base_path, image_source)
                logger.debug(f"Checking for image at full path: {full_path}")

                if os.path.isfile(full_path):
                    photo_arg = full_path
                    logger.debug(f"Found image at path: {full_path}")
                else:
                    logger.warning(f"Image not found at path: {full_path}")
                    # Try alternative path formats
                    if "/" in image_source:
                        # Try extracting just the filename
                        filename = os.path.basename(image_source)
                        logger.debug(f"Trying with filename: {filename}")

                        if context == "product":
                            alt_path = os.path.join(
                                base_path, "uploads", "product_images", filename
                            )
                        elif context == "category":
                            alt_path = os.path.join(
                                base_path, "uploads", "category_images", filename
                            )
                        else:
                            alt_path = os.path.join(base_path, "uploads", filename)

                        logger.debug(f"Checking alternative path: {alt_path}")
                        if os.path.isfile(alt_path):
                            photo_arg = alt_path
                            logger.debug(f"Found image at alternative path: {alt_path}")

                logger.debug(f"Using uploads path for {context} image: {photo_arg}")
            elif os.path.isfile(image_source):
                # Use local file path
                photo_arg = image_source
                logger.debug(f"Using local file for {context} image: {photo_arg}")
            elif os.path.isfile(os.path.join(PRODUCT_IMAGES_FOLDER, image_source)):
                # Use path relative to PRODUCT_IMAGES_FOLDER
                photo_arg = os.path.join(PRODUCT_IMAGES_FOLDER, image_source)
                logger.debug(
                    f"Using product images folder file for {context} image: {photo_arg}"
                )
            else:
                # Try to find the file in the appropriate folder based on context
                base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

                # Define search folders with priority based on context
                if context == "product":
                    search_folders = [
                        os.path.join(base_path, "uploads", "product_images"),
                        os.path.join(base_path, "uploads"),
                    ]
                    logger.debug(
                        f"Using product context - searching product folders first"
                    )
                elif context == "category":
                    search_folders = [
                        os.path.join(base_path, "uploads", "category_images"),
                        os.path.join(base_path, "uploads"),
                    ]
                    logger.debug(
                        f"Using category context - searching category folders first"
                    )
                else:
                    search_folders = [os.path.join(base_path, "uploads")]
                    logger.debug(
                        f"Using generic context - searching uploads folder only"
                    )

                # Get filename from image_source
                filename = os.path.basename(image_source)
                found = False

                # Search in context-specific folders with priority
                for folder in search_folders:
                    if not os.path.exists(folder):
                        logger.debug(f"Folder {folder} doesn't exist, skipping")
                        continue

                    # Try exact match first
                    direct_path = os.path.join(folder, filename)
                    logger.debug(f"Checking for exact match: {direct_path}")
                    if os.path.isfile(direct_path):
                        photo_arg = direct_path
                        logger.debug(
                            f"Found {context} image by exact filename: {photo_arg}"
                        )
                        found = True
                        break

                    # Then try partial matches
                    if not found:
                        logger.debug(
                            f"Exact match not found, trying partial matches in {folder}"
                        )
                        try:
                            for file in os.listdir(folder):
                                if filename in file and os.path.isfile(
                                    os.path.join(folder, file)
                                ):
                                    photo_arg = os.path.join(folder, file)
                                    logger.debug(
                                        f"Found {context} image by partial match: {photo_arg}"
                                    )
                                    found = True
                                    break
                        except Exception as e:
                            logger.error(f"Error listing directory {folder}: {e}")

                    if found:
                        break

                if not found:
                    logger.warning(f"Could not find {context} image for {image_source}")
                    photo_arg = None
        else:
            logger.warning(
                f"Invalid image_source type after processing: {type(image_source).__name__}"
            )
    else:
        logger.debug(
            f"No image processing for {context} message, using text-only format"
        )

    try:
        # Enforce context-specific image rules

        # Rule 1: Product view - only show image if product has a valid image
        if context == "product":
            # If we're showing a product and there's no valid image, always use text-only
            if not photo_arg:
                logger.debug("Product has no image - using text-only display")
                try:
                    # Always delete and send new message for product without image
                    # This ensures we don't keep showing the category image for a product
                    try:
                        await bot.delete_message(chat_id=chat_id, message_id=message_id)
                    except Exception as delete_error:
                        logger.warning(f"Could not delete message: {delete_error}")

                    # Send a new text-only message
                    await bot.send_message(
                        chat_id=chat_id,
                        text=text,
                        reply_markup=keyboard,
                        parse_mode="HTML",
                    )
                    logger.debug(
                        "Successfully displayed product without image as text-only"
                    )
                    return
                except Exception as e:
                    logger.warning(f"Error switching to text-only mode: {e}")
                    # Continue with normal flow as fallback
            else:
                # Product has an image - always recreate message to ensure proper image display
                logger.debug(
                    "Product has image - recreating message with product image"
                )
                try:
                    await bot.delete_message(chat_id=chat_id, message_id=message_id)
                    caption = text[:1024]  # Telegram caption limit
                    if len(text) > 1024:
                        caption = text[:1020] + "..."

                    if os.path.isfile(photo_arg):
                        media_photo = FSInputFile(path=photo_arg)
                    else:
                        media_photo = photo_arg

                    await bot.send_photo(
                        chat_id=chat_id,
                        photo=media_photo,
                        caption=caption,
                        reply_markup=keyboard,
                        parse_mode="HTML",
                    )
                    logger.debug("Successfully displayed product with image")
                    return
                except Exception as recreate_error:
                    logger.warning(
                        f"Error recreating product photo message: {recreate_error}"
                    )
                    # Continue with normal flow as fallback

        # Rule 2: Only show images in appropriate contexts
        # If not product or category context, always use text-only
        elif context != "category":
            # If we're not in a category context, don't show images
            logger.debug(f"Non-category context '{context}' - using text-only display")
            try:
                # Always use text-only for non-category, non-product contexts
                try:
                    await bot.delete_message(chat_id=chat_id, message_id=message_id)
                except Exception as delete_error:
                    logger.warning(f"Could not delete message: {delete_error}")

                # Send a new text-only message
                await bot.send_message(
                    chat_id=chat_id, text=text, reply_markup=keyboard, parse_mode="HTML"
                )
                logger.debug(f"Successfully displayed {context} as text-only")
                return
            except Exception as e:
                logger.warning(f"Error switching to text-only mode: {e}")
                # Continue with normal flow as fallback

        # If we get here and have a valid photo_arg, display with image
        if photo_arg is not None:
            # We have a valid image to display
            caption = text[:1024]  # Telegram caption limit
            if len(text) > 1024:
                caption = text[:1020] + "..."
                logger.warning("Catalog text truncated for photo caption.")

            if is_photo:
                # Current message is photo, edit it
                try:
                    # For local files, we need to use FSInputFile
                    if os.path.isfile(photo_arg):
                        media_photo = FSInputFile(path=photo_arg)
                        logger.debug(f"Using FSInputFile for local file: {photo_arg}")
                    else:
                        media_photo = photo_arg
                        logger.debug(f"Using direct photo arg: {photo_arg}")

                    await bot.edit_message_media(
                        chat_id=chat_id,
                        message_id=message_id,
                        media=types.InputMediaPhoto(
                            media=media_photo, caption=caption, parse_mode="HTML"
                        ),
                        reply_markup=keyboard,
                    )
                    logger.debug(f"Edited existing photo message for {context}")
                except Exception as e:
                    logger.warning(
                        f"Could not edit photo message: {e}. Sending new photo."
                    )
                    try:
                        await bot.delete_message(chat_id=chat_id, message_id=message_id)
                    except Exception:
                        pass  # Ignore deletion errors

                    # Also handle FSInputFile here for new photo
                    if os.path.isfile(photo_arg):
                        media_photo = FSInputFile(path=photo_arg)
                        logger.debug(
                            f"Using FSInputFile for local file (new send): {photo_arg}"
                        )
                    else:
                        media_photo = photo_arg
                        logger.debug(f"Using direct photo arg (new send): {photo_arg}")

                    await bot.send_photo(
                        chat_id=chat_id,
                        photo=media_photo,
                        caption=caption,
                        reply_markup=keyboard,
                        parse_mode="HTML",
                    )
            else:
                # Current message is text, replace it with photo
                try:
                    await bot.delete_message(chat_id=chat_id, message_id=message_id)
                except Exception as e:
                    logger.warning(f"Could not delete message: {e}")

                # Handle FSInputFile for local photos
                if os.path.isfile(photo_arg):
                    media_photo = FSInputFile(path=photo_arg)
                    logger.debug(
                        f"Using FSInputFile for local file (text->photo): {photo_arg}"
                    )
                else:
                    media_photo = photo_arg
                    logger.debug(f"Using direct photo arg (text->photo): {photo_arg}")

                await bot.send_photo(
                    chat_id=chat_id,
                    photo=media_photo,
                    caption=caption,
                    reply_markup=keyboard,
                    parse_mode="HTML",
                )
                logger.debug(f"Sent new photo message for {context}")
        else:
            # No valid image, use text-only message
            try:
                await bot.edit_message_text(
                    text=text,
                    chat_id=chat_id,
                    message_id=message_id,
                    reply_markup=keyboard,
                    parse_mode="HTML",
                )
                logger.debug(f"Used text-only message for {context} (no valid image)")
            except Exception as edit_error:
                logger.warning(
                    f"Could not edit message: {edit_error}. Sending new message."
                )
                try:
                    await bot.delete_message(chat_id=chat_id, message_id=message_id)
                except Exception:
                    pass  # Ignore deletion errors

                await bot.send_message(
                    chat_id=chat_id, text=text, reply_markup=keyboard, parse_mode="HTML"
                )
    except Exception as e:
        logger.error(f"Error sending catalog message: {e}")
        # Final fallback attempt
        try:
            await bot.edit_message_text(
                text=text,
                chat_id=chat_id,
                message_id=message_id,
                reply_markup=keyboard,
                parse_mode="HTML",
            )
        except Exception as final_e:
            logger.error(f"Final fallback failed: {final_e}")
            # Last resort - try to send a new message
            try:
                await bot.send_message(
                    chat_id=chat_id, text=text, reply_markup=keyboard, parse_mode="HTML"
                )
            except Exception:
                pass  # Nothing more we can do


async def handle_category_error(
    callback_query: types.CallbackQuery, reason: str = "An error occurred"
):
    """Handles errors during category browsing."""
    logger.error(
        f"Category handling error for user {callback_query.from_user.id}: {reason}"
    )
    fallback_keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔄 Try Again Browsing", callback_data="browse_products"
                )
            ],
            [InlineKeyboardButton(text="🏠 Main Menu", callback_data="return_to_main")],
        ]
    )
    error_text = (
        "❌ <b>• SYSTEM ERROR •</b>\n\n"
        "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
        "<b>PROCESSING ISSUE</b>\n"
        "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
        f"<i>{reason} while processing your request</i>\n"
        f"<i>Please try again or contact support if needed</i>\n\n"
        "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>"
    )
    try:
        await safe_edit_message(
            callback_query.message,
            error_text,
            reply_markup=fallback_keyboard,
            parse_mode="HTML",
        )
    except Exception as e:
        logger.error(f"Failed to send category error fallback message: {e}")


@router.callback_query(F.data.startswith("browse_all_products"))
async def browse_all_products(callback_query: types.CallbackQuery):
    """Browse all available products."""
    try:
        # Check if this is a paginated request
        data_parts = callback_query.data.split(":")
        page = 1

        # Handle pagination requests (format: browse_all_products:page)
        if len(data_parts) > 1 and data_parts[1].isdigit():
            page = int(data_parts[1])

        await callback_query.answer()
        products = get_products()  # Get all products

        if not products:
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Categories",
                            callback_data="browse_products",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🏠 Main Menu", callback_data="return_to_main"
                        )
                    ],
                ]
            )
            await safe_edit_message(
                callback_query.message,
                "<b>• ALL PRODUCTS •</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>INVENTORY STATUS</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
                "<i>No products are currently available</i>\n"
                "<i>New items are added regularly</i>\n"
                "<i>Please check back soon for updates</i>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>",
                reply_markup=keyboard,
                parse_mode="HTML",
            )
            return

        # Paginate products
        items_per_page = 5  # Show 5 products per page
        paginated_products, total_pages = paginate_items(products, page, items_per_page)
        logger.info(
            f"Displaying page {page}/{total_pages} for all products ({len(products)} products total)"
        )

        inline_keyboard = []

        # Clean product buttons with professional styling
        for i, product in enumerate(paginated_products, 1):
            product_id = product.get("_id") or product.get("id")
            product_name = str(product.get("name", "Premium Product"))
            price = product.get("price", 0) or 0

            if not product_id:
                logger.warning(
                    f"Product '{product_name}' missing ID in 'all products' list, skipping."
                )
                continue

            # Clean button text with price and numbering
            button_text = f"{i}. {product_name} • ${price:.2f}"

            # Truncate button text if too long to prevent layout issues
            if len(button_text) > 45:
                truncated_name = product_name[:32] + "..."
                button_text = f"{i}. {truncated_name} • ${price:.2f}"

            button = InlineKeyboardButton(
                text=button_text, callback_data=f"select_product:{product_id}"
            )

            # Single-column layout for better readability
            inline_keyboard.append([button])

        # Add pagination buttons if more than one page
        if total_pages > 1:
            pagination_row = []

            # Previous page button
            if page > 1:
                pagination_row.append(
                    InlineKeyboardButton(
                        text="🔙 Previous",
                        callback_data=f"browse_all_products:{page - 1}",
                    )
                )

            # Page indicator
            pagination_row.append(
                InlineKeyboardButton(
                    text=f"📄 {page}/{total_pages}", callback_data="noop"
                )
            )

            # Next page button
            if page < total_pages:
                pagination_row.append(
                    InlineKeyboardButton(
                        text="Next ▶️", callback_data=f"browse_all_products:{page + 1}"
                    )
                )

            inline_keyboard.append(pagination_row)

        # Clean navigation buttons with organized layout
        navigation_row_1 = [
            InlineKeyboardButton(
                text="🔙 Back to Categories", callback_data="browse_products"
            ),
            InlineKeyboardButton(
                text="🛒 View Cart", callback_data="view_cart"
            )
        ]

        navigation_row_2 = [
            InlineKeyboardButton(
                text="🏠 Main Menu", callback_data="return_to_main"
            )
        ]

        inline_keyboard.append(navigation_row_1)
        inline_keyboard.append(navigation_row_2)
        keyboard = InlineKeyboardMarkup(inline_keyboard=inline_keyboard)

        # Build clean detailed text list with professional styling
        catalog_text = "<b>• ALL PRODUCTS •</b>\n\n"
        catalog_text += "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
        catalog_text += "<b>COMPLETE COLLECTION</b>\n"
        catalog_text += "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
        catalog_text += f"<b>Total Products:</b> <code>{len(products)}</code>\n"
        catalog_text += f"<b>Showing:</b> <code>{len(paginated_products)} items</code>\n"
        if total_pages > 1:
            catalog_text += f"<b>Page:</b> <code>{page} of {total_pages}</code>\n"
        catalog_text += f"\n<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"

        # Clean product listing with structured hierarchy
        for i, product in enumerate(paginated_products, 1):
            name = product.get("name", "Premium Product")
            price = product.get("price", 0) or 0
            description = (
                product.get("description", "Exclusive digital product") or "Exclusive digital product"
            )
            # Get first line and limit to 65 characters for better readability
            first_line = description.split("\n", 1)[0]
            desc_short = first_line[:65] + ("..." if len(first_line) > 65 else "")

            catalog_text += f"<b>{i}. {name}</b>\n"
            catalog_text += f"   <i>{desc_short}</i>\n"
            catalog_text += f"   <b>💎 Price:</b> <code>${price:.2f}</code>\n"

            # Add subtle separator between products (except last one)
            if i < len(paginated_products):
                catalog_text += f"   ┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\n"

        # Clean footer with clear call-to-action
        catalog_text += f"\n<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
        catalog_text += f"<i>Select any product below to view details and purchase options</i>"

        # Use the generic send/edit function
        is_photo = callback_query.message.photo is not None

        # For the all products view, we should always use text-only mode
        # This ensures we don't show product images in the category view
        if is_photo:
            # If current message has an image, delete it and send a new text-only message
            try:
                await callback_query.bot.delete_message(
                    chat_id=callback_query.message.chat.id,
                    message_id=callback_query.message.message_id,
                )
                await callback_query.bot.send_message(
                    chat_id=callback_query.message.chat.id,
                    text=catalog_text,
                    reply_markup=keyboard,
                    parse_mode="HTML",
                )
                logger.info(
                    "Successfully switched from photo to text-only for all products view"
                )
            except Exception as e:
                logger.error(f"Error switching to text-only for all products: {e}")
                # Fall back to standard method
                await send_catalog_message(
                    callback_query.bot,
                    callback_query.message.chat.id,
                    callback_query.message.message_id,
                    catalog_text,
                    keyboard,
                    image_source=None,  # Don't use any image for the all products view
                    is_photo=is_photo,
                    context="all_products",  # Specify that this is an all products context
                )
        else:
            # Current message is already text-only, just update it
            # Always use text-only for all products view
            await send_catalog_message(
                callback_query.bot,
                callback_query.message.chat.id,
                callback_query.message.message_id,
                catalog_text,
                keyboard,
                image_source=None,  # Don't use any image for the all products view
                is_photo=is_photo,
                context="all_products",  # Specify that this is an all products context
            )

    except Exception as e:
        logger.exception(f"Error browsing all products: {e}")
        # Generic error handling for browsing
        await handle_category_error(callback_query, "Failed to load all products")


@router.callback_query(F.data.startswith("select_product:"))
async def select_product(callback_query: types.CallbackQuery):
    """Displays detailed information about a selected product."""
    chat_id = callback_query.message.chat.id
    message_id = callback_query.message.message_id

    try:
        await callback_query.answer()  # Acknowledge the callback immediately

        # Extract product ID from callback data
        try:
            product_id_str = callback_query.data.split(":")[1]
            product_id = _get_product_id_from_string(product_id_str)
        except (IndexError, ValueError) as e:
            logger.error(f"Invalid callback data format in select_product: {callback_query.data}")
            await callback_query.answer("❌ Invalid product selection!", show_alert=True)
            return

        # Get product details
        product = get_product(product_id)

        if not product:
            logger.warning(f"Product not found: {product_id_str}")
            await safe_edit_message(
                callback_query.message,
                "❌ <b>• PRODUCT UNAVAILABLE •</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>ITEM NOT FOUND</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
                "<i>This product could not be found or is no longer available</i>\n"
                "<i>Please explore our other premium offerings</i>\n"
                "<i>New products are added regularly</i>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🛍️ Browse Categories",
                                callback_data="browse_products",
                            ),
                            InlineKeyboardButton(
                                text="🔍 All Products",
                                callback_data="browse_all_products",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Main Menu",
                                callback_data="return_to_main",
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
            return

        # Create enhanced product details text with better formatting
        name = product.get("name", "Premium Product")
        description = (
            product.get("description", "Exclusive digital product with premium features")
            or "Exclusive digital product with premium features"
        )
        price = product.get("price", 0) or 0
        # Get the actual ID used in DB (_id or id) for consistency in callbacks
        actual_product_id = product.get("_id") or product.get("id")

        # Get additional product information for enhanced display
        category_info = ""
        if product.get("category_id"):
            try:
                category = get_category_by_id(product.get("category_id"))
                if category:
                    category_info = f"<b>📂 Category:</b> <i>{category.get('name', 'Unknown')}</i>\n"
            except Exception:
                pass  # Ignore category lookup errors

        # Clean product details with professional hierarchy
        product_details_text = (
            f"<b>• {name.upper()} •</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>PRODUCT DETAILS</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"<b>Description:</b>\n"
            f"<i>{description}</i>\n\n"
            f"<b>💎 Price:</b> <code>${price:.2f}</code>\n"
            f"{category_info}"
            f"<b>Delivery:</b> <i>Instant Digital Download</i>\n"
            f"<b>Guarantee:</b> <i>100% Authentic Content</i>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━━━━━━━━</b>\n"
            f"<i>Ready for instant purchase and delivery</i>"
        )

        # Get the product image - EXPLICITLY specify it's a product image type
        # This is the key fix to ensure we get the product image, not category image
        image_source = extract_image_from_object(product, object_type="product")

        # Debug logging
        logger.info(f"Product image source: {image_source}")

        # Flag to track if we have a valid product image
        has_valid_image = False

        # Only try to resolve the image if we have a source
        if image_source:
            # Check if the product has an image_url field (from the provided data structure)
            if product.get("image_url"):
                logger.info(f"Product has image_url field: {product.get('image_url')}")
                image_url = product.get("image_url")

                # If it's a URL, use it directly
                if image_url.startswith(("http://", "https://")):
                    image_source = image_url
                    has_valid_image = True
                    logger.info(f"Using direct URL from image_url: {image_url}")
                # If it's a local path, resolve it
                else:
                    base_path = os.path.dirname(
                        os.path.dirname(os.path.abspath(__file__))
                    )

                    # Try direct path
                    full_path = os.path.join(base_path, image_url)
                    logger.info(f"Checking for image at full path: {full_path}")
                    if os.path.isfile(full_path):
                        image_source = full_path
                        has_valid_image = True
                        logger.info(f"Found image at direct path: {full_path}")
                    else:
                        # Try with just the filename in product_images folder
                        filename = os.path.basename(image_url)
                        product_images_dir = os.path.join(
                            base_path, "uploads", "product_images"
                        )
                        alt_path = os.path.join(product_images_dir, filename)
                        logger.info(
                            f"Checking for image at alternative path: {alt_path}"
                        )

                        if os.path.isfile(alt_path):
                            image_source = alt_path
                            has_valid_image = True
                            logger.info(
                                f"Found product image at alternative path: {alt_path}"
                            )
                        else:
                            # Try one more approach - check if the file exists in the uploads directory
                            uploads_dir = os.path.join(base_path, "uploads")
                            for root, _, files in os.walk(
                                uploads_dir
                            ):  # Use _ for unused variable
                                for file in files:
                                    if filename in file:
                                        found_path = os.path.join(root, file)
                                        logger.info(
                                            f"Found potential match: {found_path}"
                                        )
                                        image_source = found_path
                                        has_valid_image = True
                                        break
                                if has_valid_image:
                                    break

            # If we still don't have a valid image, try the standard checks
            if (
                not has_valid_image
                and image_source
                and not image_source.startswith(("http://", "https://", "file_id:"))
            ):
                # Check if the file exists
                if not os.path.isfile(image_source):
                    # Try to find the file in the product_images directory
                    base_path = os.path.dirname(
                        os.path.dirname(os.path.abspath(__file__))
                    )
                    product_images_dir = os.path.join(
                        base_path, "uploads", "product_images"
                    )

                    # Try with just the filename
                    filename = os.path.basename(image_source)
                    alt_path = os.path.join(product_images_dir, filename)

                    if os.path.isfile(alt_path):
                        image_source = alt_path
                        has_valid_image = True
                        logger.debug(
                            f"Found product image at alternative path: {alt_path}"
                        )
                    else:
                        # Try to find any file in the directory that contains the product ID
                        product_id_str = str(product_id)
                        if os.path.isdir(
                            product_images_dir
                        ):  # Make sure directory exists
                            for file in os.listdir(product_images_dir):
                                if product_id_str in file and os.path.isfile(
                                    os.path.join(product_images_dir, file)
                                ):
                                    image_source = os.path.join(
                                        product_images_dir, file
                                    )
                                    has_valid_image = True
                                    logger.debug(
                                        f"Found product image by ID match: {image_source}"
                                    )
                                    break
                else:
                    # The file exists at the original path
                    has_valid_image = True
                    logger.debug(
                        f"Found product image at original path: {image_source}"
                    )
            elif (
                not has_valid_image
                and image_source
                and image_source.startswith(("http://", "https://", "file_id:"))
            ):
                # URLs and file_ids are considered valid
                has_valid_image = True
                logger.debug(
                    f"Using URL/file_id as product image: {image_source[:30]}..."
                )
        else:
            # No image source provided for this product
            logger.info(f"No image source found for product {product_id}")
            has_valid_image = False
            image_source = None

        # Final debug logging after all resolution attempts
        logger.debug(
            f"Final product image source: {image_source}, valid: {has_valid_image}"
        )

        keyboard = product_detail_keyboard(actual_product_id, price)
        is_photo = callback_query.message.photo is not None

        # Always delete the current message and create a new one for product views
        # This ensures we don't keep showing category images for products
        try:
            # Delete the current message
            await callback_query.bot.delete_message(
                chat_id=chat_id, message_id=message_id
            )

            # If we have a valid product image, send it with the product details
            if has_valid_image and image_source:
                logger.info(f"Sending product with image: {image_source}")

                # Prepare caption
                caption = product_details_text[:1024]  # Telegram caption limit
                if len(product_details_text) > 1024:
                    caption = product_details_text[:1020] + "..."

                # Check if it's a local file path
                if os.path.isfile(image_source):
                    # Use FSInputFile for local files
                    photo_arg = FSInputFile(path=image_source)
                    logger.debug(f"Using FSInputFile for local file: {image_source}")
                else:
                    # Use the image source directly (URL or file_id)
                    photo_arg = image_source
                    logger.debug(f"Using direct image source: {image_source}")

                # Send the product with image
                await callback_query.bot.send_photo(
                    chat_id=chat_id,
                    photo=photo_arg,
                    caption=caption,
                    reply_markup=keyboard,
                    parse_mode="HTML",
                )
                logger.info("Successfully sent product with image")
            else:
                # No valid image, send text-only message
                logger.info("Sending product with text-only (no valid image)")
                await callback_query.bot.send_message(
                    chat_id=chat_id,
                    text=product_details_text,
                    reply_markup=keyboard,
                    parse_mode="HTML",
                )

        except Exception as e:
            logger.error(f"Error sending product details: {e}")
            # If there's an error, try the standard method as fallback

            try:
                # Fallback to standard method
                await send_catalog_message(
                    callback_query.bot,
                    chat_id,
                    message_id,
                    product_details_text,
                    keyboard,
                    image_source=(image_source if has_valid_image else None),
                    is_photo=is_photo,
                    context="product",  # Specify it's a product context
                )
            except Exception as fallback_error:
                logger.error(f"Fallback method also failed: {fallback_error}")
                # Last resort - try to send a simple message
                try:
                    await callback_query.bot.send_message(
                        chat_id=chat_id,
                        text="Sorry, there was an error displaying the product. Please try again.",
                        reply_markup=InlineKeyboardMarkup(
                            inline_keyboard=[
                                [
                                    InlineKeyboardButton(
                                        text="🔙 Back to Products",
                                        callback_data="browse_products",
                                    )
                                ]
                            ]
                        ),
                    )
                except Exception:
                    pass  # Nothing more we can do

    except Exception as e:
        logger.exception(
            f"Error selecting product (ID str: {callback_query.data.split(':')[1] if ':' in callback_query.data else 'unknown'}):",
            exc_info=e,
        )
        await handle_category_error(callback_query, "Could not display product details")


# --- Cart Management ---


@router.callback_query(F.data.startswith("add_to_cart:"))
async def add_to_cart(callback_query: types.CallbackQuery, state: FSMContext):
    """Add a product to the user's cart."""
    try:
        try:
            product_id_str = callback_query.data.split(":")[1]
            product_id = _get_product_id_from_string(product_id_str)
            user_id = callback_query.from_user.id
        except (IndexError, ValueError) as e:
            logger.error(f"Invalid callback data format in add_to_cart: {callback_query.data}")
            await callback_query.answer("❌ Invalid product selection!", show_alert=True)
            return

        product = get_product(product_id)

        if not product:
            logger.warning(f"Add to cart failed: Product {product_id_str} not found.")
            await callback_query.answer("❌ Product not available!", show_alert=True)
            # Optionally edit message back to browse
            await safe_edit_message(
                callback_query.message, "This product is no longer available."
            )
            return

        # Get the actual ID used in DB (_id or id) for cart storage
        actual_product_id = product.get("_id") or product.get("id")
        if not actual_product_id:
            logger.error(f"Product {product.get('name')} has no usable ID for cart.")
            await callback_query.answer(
                "❌ Error adding product (data issue).", show_alert=True
            )
            return

        cart = get_or_create_cart(user_id)
        cart_items = cart.get("items", [])

        # Check if product already in cart (compare actual IDs as strings for safety)
        product_in_cart = any(
            str(item.get("product_id")) == str(actual_product_id) for item in cart_items
        )

        if product_in_cart:
            logger.info(
                f"Product {actual_product_id} already in cart for user {user_id}."
            )
            await callback_query.answer("ℹ️ Item already in your cart", show_alert=False)
            # Optionally offer view cart button
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🛒 View Cart", callback_data="view_cart"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🛍️ Continue Shopping", callback_data="browse_products"
                        )
                    ],
                ]
            )
            await safe_edit_message(
                callback_query.message,
                f"ℹ️ <b>{product.get('name')}</b> is already in your cart!",
                reply_markup=keyboard,
                parse_mode="HTML",
            )

        else:
            # Check if this is a line-based product that needs quantity selection
            if product.get("is_line_based", False):
                # Use consolidated line product manager for validation and workflow
                from utils.line_product_manager import line_product_manager
                from states.states import OrderStates

                # Validate the product purchase first with user ID for shared inventory
                validation = await line_product_manager.validate_line_product_purchase(
                    actual_product_id, 1, user_id=callback_query.from_user.id  # Check for minimum quantity
                )

                if not validation["valid"]:
                    await callback_query.answer(f"❌ {validation['error']}", show_alert=True)
                    return

                # Store product info in state for quantity selection
                await safe_update_data(state,
                    line_product_id=actual_product_id,
                    line_product_data=product,
                    action_type="add_to_cart"
                )

                # Calculate user-specific availability once for consistency between text and buttons
                allow_shared_inventory = product.get("allow_shared_inventory", False)
                user_id = callback_query.from_user.id

                if allow_shared_inventory:
                    from database.operations import get_available_lines_for_user
                    total_lines = product.get("total_lines", 0)

                    # Clear cache to ensure fresh data for both text and buttons
                    line_product_manager.clear_validation_cache(actual_product_id, user_id)

                    user_available_lines = get_available_lines_for_user(user_id, actual_product_id, total_lines)
                    available_lines = len(user_available_lines)
                else:
                    available_lines = validation["available_stock"]

                # Use cart display with pre-calculated availability
                quantity_message = line_product_manager.create_cart_quantity_display_with_availability(
                    product, available_lines, allow_shared_inventory, user_id=user_id
                )

                keyboard = line_product_manager.create_quantity_selection_keyboard_with_availability(
                    actual_product_id,
                    validation["max_quantity_per_order"],
                    available_lines,
                    allow_shared_inventory,
                    user_id=user_id
                )

                await safe_edit_message(
                    callback_query.message,
                    quantity_message,
                    reply_markup=keyboard,
                    parse_mode="HTML"
                )

                await state.set_state(OrderStates.selecting_quantity)
                return
            elif product.get("is_exclusive_single_use", False):
                # Handle exclusive single-use products
                from utils.exclusive_product_manager import exclusive_product_manager
                from utils.exclusive_product_db_operations import ExclusiveProductDBOperations

                # Validate the exclusive product purchase
                validation = ExclusiveProductDBOperations.check_user_can_purchase_exclusive(
                    actual_product_id, user_id
                )

                if not validation["can_purchase"]:
                    await callback_query.answer(f"❌ {validation['reason']}", show_alert=True)
                    return

                # Create exclusive product cart item
                try:
                    exclusive_item = exclusive_product_manager.create_exclusive_product_cart_item(
                        product, actual_product_id
                    )
                except Exception as e:
                    logger.error(f"Error creating exclusive cart item: {e}")
                    await callback_query.answer("❌ Error adding exclusive product to cart!", show_alert=True)
                    return

                # Add to cart
                cart = get_or_create_cart(user_id)
                cart_items = cart.get("items", [])

                # Check if exclusive product is already in cart
                for item in cart_items:
                    if (item.get("product_id") == actual_product_id and
                        item.get("is_exclusive_single_use", False)):
                        await callback_query.answer("📄 This exclusive product is already in your cart!", show_alert=True)
                        return

                cart_items.append(exclusive_item)
                update_cart(user_id, cart_items)

                # Show success message with exclusive theming
                from utils.exclusive_product_manager import ExclusiveProductTheme
                success_message = (
                    f"{ExclusiveProductTheme.create_header('ADDED TO CART', 'EXCLUSIVE PRODUCT')}\n"
                    f"{ExclusiveProductTheme.EMOJIS['exclusive_product']} <b>{product.get('name', 'Product')}</b>\n"
                    f"{ExclusiveProductTheme.format_price(product.get('price', 0))}\n\n"
                    f"{ExclusiveProductTheme.EMOJIS['success']} <b>Successfully added to cart!</b>\n\n"
                    f"{ExclusiveProductTheme.create_section_break()}\n"
                    f"🔒 <i>This exclusive product will be reserved for you until checkout.</i>"
                )

                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🛒 View Cart",
                                callback_data="view_cart"
                            ),
                            InlineKeyboardButton(
                                text="💰 Checkout",
                                callback_data="checkout"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🛍️ Continue Shopping",
                                callback_data="browse_products"
                            )
                        ]
                    ]
                )

                await safe_edit_message(
                    callback_query.message,
                    success_message,
                    reply_markup=keyboard,
                    parse_mode="HTML"
                )
                return
            else:
                # Add regular product to cart using CartItemFactory
                from utils.cart_item_factory import cart_item_factory

                try:
                    new_item = cart_item_factory.create_cart_item(
                        product=product,
                        product_id=actual_product_id,
                        quantity=1
                    )
                    cart_items.append(new_item)
                    update_cart(user_id, cart_items)
                except ValueError as e:
                    logger.error(f"Failed to create cart item for product {actual_product_id}: {e}")
                    await callback_query.answer("❌ Error adding product to cart.", show_alert=True)
                    return
                logger.info(
                    f"Added product {actual_product_id} to cart for user {user_id}. Cart size: {len(cart_items)}"
                )
                await callback_query.answer("✅ Added to cart!", show_alert=False)

                success_message = (
                    f"✅ <b>\u2022 ADDED TO CART \u2022</b> ✅\n\n"
                    f"<b>✨ {product.get('name')} ✨</b>\n\n"
                    f"<i>Item added successfully!</i>"
                )
                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🛒 View Cart", callback_data="view_cart"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🛍️ Continue Shopping", callback_data="browse_products"
                            )
                        ],
                    ]
                )
                await safe_edit_message(
                    callback_query.message,
                    success_message,
                    reply_markup=keyboard,
                    parse_mode="HTML",
                )

    except Exception as e:
        logger.exception(
            f"Error adding product to cart (ID str: {callback_query.data.split(':')[1]}):",
            exc_info=e,
        )
        await callback_query.answer(
            "❌ Error adding to cart. Please try again.", show_alert=True
        )
        # Provide error feedback to user
        error_keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔄 Try Again", callback_data=callback_query.data
                    )
                ],  # Allow retry
                [
                    InlineKeyboardButton(
                        text="🛍️ Browse Products", callback_data="browse_products"
                    )
                ],
            ]
        )
        await safe_edit_message(
            callback_query.message,
            "❌ <b>\u2022 ERROR \u2022</b> ❌\n\n<i>Could not add item to cart. Please try again.</i>",
            reply_markup=error_keyboard,
            parse_mode="HTML",
        )


@router.message(Command("cart"))
async def cmd_view_cart(message: types.Message):
    """Handle the /cart command to show the user's cart."""
    await view_cart_message(message)


@router.callback_query(F.data == "view_cart")
async def view_cart_callback(callback_query: types.CallbackQuery):
    """Handle the 'View Cart' button callback."""
    user_id = callback_query.from_user.id
    try:
        # Acknowledge the callback immediately to provide feedback
        await callback_query.answer("Loading your cart...")

        # Check if the message still exists before trying to edit it
        try:
            # Re-use the logic from view_cart_message, adapting for callback query
            await view_cart_logic(
                callback_query.message,
                user_id,
                callback_query.bot,
                edit_mode=True,
            )
        except Exception as message_err:
            logger.error(f"Error in view_cart_logic for user {user_id}: {message_err}")
            # If there's an error with the message, try sending a new one
            await send_cart_error_message(
                callback_query.bot,
                callback_query.message.chat.id,
                "Error displaying your cart. Please try again.",
                edit_mode=False,
            )
    except Exception as e:
        logger.exception(
            f"Error in view_cart_callback for user {user_id}:",
            exc_info=e,
        )
        # Try to show an alert if possible
        try:
            await callback_query.answer(
                "❌ Error displaying cart. Please try again.", show_alert=True
            )
        except Exception:
            # If we can't even show an alert, just log it
            logger.error(f"Failed to show error alert to user {user_id}")

        # Try to send a fallback error message
        try:
            await callback_query.message.answer(
                "❌ <b>Error displaying cart</b>\n\nPlease try again or return to the main menu.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔄 Try Again", callback_data="view_cart"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Main Menu", callback_data="return_to_main"
                            )
                        ],
                    ]
                ),
            )
        except Exception as fallback_err:
            logger.error(f"Failed to send fallback error message: {fallback_err}")


async def view_cart_message(message: types.Message):
    """Send cart view as a new message (typically for /cart command)."""
    await view_cart_logic(message, message.from_user.id, message.bot, edit_mode=False)


async def view_cart_logic(
    target_message: types.Message, user_id: int, bot: Bot, edit_mode: bool
):
    """Core logic to display the cart, either editing or sending new."""
    try:
        # Add better error logging for database operations
        try:
            cart = get_or_create_cart(user_id)
            if not cart:
                logger.error(f"Failed to retrieve cart for user {user_id}")
                raise ValueError("Could not retrieve cart data")

            logger.debug(f"Retrieved cart for user {user_id}: {bool(cart)}")
        except Exception as db_err:
            logger.error(f"Database error getting cart for user {user_id}: {db_err}")
            # Create a fallback message for database errors
            await send_cart_error_message(
                bot,
                target_message.chat.id,
                "We're having trouble accessing your cart data. Please try again later.",
                edit_mode=edit_mode,
                target_message=target_message,
            )
            return  # Exit early on database errors

        cart_items = cart.get("items", [])

        # Use consistent theming for cart header
        from utils.line_product_manager import LineProductTheme
        cart_text = LineProductTheme.create_header("YOUR CART", "SHOPPING CART")
        keyboard_buttons = []
        keyboard = None  # Initialize keyboard variable to avoid UnboundLocalError

        if not cart_items:
            cart_text += f"{LineProductTheme.EMOJIS['info']} <i>Your cart is currently empty!</i>\n\n"
            cart_text += LineProductTheme.create_section_break()
            cart_text += "<i>Start shopping to add items to your cart</i>"

            keyboard_buttons = [
                [
                    InlineKeyboardButton(
                        text="✨ Browse Products ✨", callback_data="browse_products"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🏠 Main Menu", callback_data="return_to_main"
                    )
                ],
            ]
        else:
            cart_text += f"{LineProductTheme.EMOJIS['cart']} <b>Your Items:</b>\n\n"
            total = 0.0

            # Add validation for each cart item
            valid_items = []
            for i, item in enumerate(cart_items, 1):
                name = item.get("name", "Product")
                # Note: is_line_based check is handled in delivery processing

                try:
                    # More robust price handling
                    price_raw = item.get("price")
                    if price_raw is None:
                        price = 0.0
                        logger.warning(f"Item '{name}' has None price, defaulting to 0")
                    else:
                        try:
                            price = float(price_raw)
                        except (ValueError, TypeError):
                            price = 0.0
                            logger.warning(
                                f"Item '{name}' has invalid price format: {price_raw}"
                            )

                    # Only add items with valid data
                    item_copy = (
                        item.copy()
                    )  # Create a copy to avoid modifying the original
                    item_copy["price"] = price  # Ensure price is a float
                    valid_items.append(item_copy)

                    total += price

                    # Use consolidated cart item display
                    cart_text += LineProductTheme.format_cart_item(item_copy)

                    cart_text += "\n\n"
                except Exception as item_err:
                    # Handle any unexpected errors with item processing
                    logger.error(
                        f"Error processing cart item '{name}' for user {user_id}: {item_err}"
                    )
                    cart_text += f"<b>{i}. {name}</b> - <code>Price error</code>\n"

            # If we found invalid items, update the cart
            if len(valid_items) != len(cart_items):
                logger.info(
                    f"Fixing {len(cart_items) - len(valid_items)} invalid items in cart for user {user_id}"
                )
                try:
                    update_success = update_cart(user_id, valid_items)
                    if not update_success:
                        logger.warning(f"Cart update returned False for user {user_id}")
                except Exception as update_err:
                    logger.error(
                        f"Failed to update cart with valid items: {update_err}"
                    )

            # Format the total with consistent currency formatting
            try:
                # Try to import format_currency from utils
                from utils.helpers import format_currency

                formatted_total = format_currency(total)
            except ImportError:
                # Fallback to standard formatting if helper not available
                formatted_total = f"${total:.2f}"

            # Use themed divider and total display
            cart_text += f"{LineProductTheme.HEADER_DIVIDER}\n"
            cart_text += f"{LineProductTheme.EMOJIS['price']} <b>Total:</b> <code>{formatted_total}</code>\n"

            # Add user balance if available
            try:
                balance = get_user_balance(user_id)
                try:
                    from utils.helpers import format_currency

                    formatted_balance = format_currency(balance)
                except ImportError:
                    formatted_balance = f"${balance:.2f}"

                cart_text += (
                    f"💳 <b>Your Balance:</b> <code>{formatted_balance}</code>\n"
                )

                # Add warning if balance is insufficient
                if balance < total:
                    cart_text += f"{LineProductTheme.EMOJIS['warning']} <b>Insufficient balance for checkout</b>\n"
            except Exception as balance_err:
                logger.warning(
                    f"Could not retrieve balance for user {user_id}: {balance_err}"
                )

            cart_text += f"{LineProductTheme.HEADER_DIVIDER}\n\n"
            cart_text += LineProductTheme.create_section_break()
            cart_text += "<i>Manage your items or proceed to checkout</i>"

            # Use the imported cart_keyboard function
            try:
                keyboard = cart_keyboard()
            except Exception as kb_err:
                logger.warning(
                    f"Error creating cart keyboard: {kb_err}, using fallback"
                )
                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="💳 Checkout", callback_data="checkout"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🗑️ Clear Cart", callback_data="clear_cart"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🛍️ Continue Shopping",
                                callback_data="browse_products",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Main Menu", callback_data="return_to_main"
                            )
                        ],
                    ]
                )

        # Ensure keyboard is properly formatted
        final_keyboard = (
            keyboard
            if isinstance(keyboard, InlineKeyboardMarkup)
            else InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
        )

        # Handle message sending or editing with better error information
        if edit_mode:
            try:
                await safe_edit_message(
                    target_message,
                    cart_text,
                    reply_markup=final_keyboard,
                    parse_mode="HTML",
                )
                logger.debug(f"Successfully edited cart message for user {user_id}")
            except Exception as edit_err:
                logger.error(f"Failed to edit cart message: {edit_err}")
                # Fall back to sending new message
                edit_mode = False

        if not edit_mode:
            try:
                await bot.send_message(
                    chat_id=target_message.chat.id,
                    text=cart_text,
                    reply_markup=final_keyboard,
                    parse_mode="HTML",
                )
                logger.debug(f"Successfully sent new cart message for user {user_id}")
            except Exception as send_err:
                logger.error(f"Failed to send new cart message: {send_err}")
                await send_cart_error_message(
                    bot,
                    target_message.chat.id,
                    "Error displaying your cart. Please try again later.",
                )

    except TelegramBadRequest as e:
        if "message is not modified" in str(e).lower():
            logger.info(f"Cart view message for user {user_id} was not modified.")
        elif "message to edit not found" in str(e).lower() and edit_mode:
            logger.warning(
                f"Message to edit cart view for user {user_id} not found. Sending new."
            )
            await view_cart_logic(target_message, user_id, bot, edit_mode=False)
        else:
            logger.exception(
                f"Telegram error displaying cart for user {user_id}: {str(e)}"
            )
            await send_cart_error_message(
                bot,
                target_message.chat.id,
                "Error displaying your cart. Please try again later.",
                edit_mode=False,
            )
    except Exception as e:
        logger.exception(f"Error displaying cart for user {user_id}: {str(e)}")
        await send_cart_error_message(
            bot,
            target_message.chat.id,
            "Could not display your cart at this time. Please try again later.",
            edit_mode=False,
        )


async def send_cart_error_message(
    bot: Bot, chat_id: int, message: str, edit_mode=False, target_message=None
):
    """Helper function to send cart error messages with a consistent format."""
    error_text = (
        "❌ <b>\u2022 CART ERROR \u2022</b> ❌\n\n"
        f"<i>{message}</i>\n\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        "<i>You can try again or browse our products.</i>\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>"
    )

    error_keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [InlineKeyboardButton(text="🔄 Try Again", callback_data="view_cart")],
            [
                InlineKeyboardButton(
                    text="🛍️ Browse Products", callback_data="browse_products"
                )
            ],
            [InlineKeyboardButton(text="🏠 Main Menu", callback_data="return_to_main")],
        ]
    )

    try:
        if edit_mode and target_message:
            await safe_edit_message(
                target_message,
                error_text,
                reply_markup=error_keyboard,
                parse_mode="HTML",
            )
        else:
            await bot.send_message(
                chat_id=chat_id,
                text=error_text,
                reply_markup=error_keyboard,
                parse_mode="HTML",
            )
    except Exception as e:
        logger.error(f"Failed to send cart error message: {e}")
        # Last resort - try a very simple message
        try:
            await bot.send_message(
                chat_id=chat_id, text="Error displaying cart. Please try again later."
            )
        except Exception:
            pass  # Nothing more we can do


@router.callback_query(F.data == "clear_cart")
async def clear_cart_handler(callback_query: types.CallbackQuery):
    """Handle the 'Clear Cart' button."""
    user_id = callback_query.from_user.id
    try:
        # Acknowledge the callback immediately
        await callback_query.answer("Processing...")

        # Clear the cart with error handling
        try:
            clear_success = clear_cart(user_id)
            if not clear_success:
                logger.warning(f"clear_cart returned False for user {user_id}")
                raise ValueError("Failed to clear cart")

            logger.info(f"Successfully cleared cart for user {user_id}")
        except Exception as clear_err:
            logger.error(
                f"Database error clearing cart for user {user_id}: {clear_err}"
            )
            await callback_query.answer(
                "❌ Error clearing your cart. Please try again.", show_alert=True
            )
            return

        # Show success message
        await callback_query.answer("🗑️ Cart cleared!", show_alert=False)

        # Create keyboard for next actions
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="✨ Browse Products ✨", callback_data="browse_products"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🏠 Return to Main Menu", callback_data="return_to_main"
                    )
                ],
            ]
        )

        # Update the message with success notification
        try:
            await safe_edit_message(
                callback_query.message,
                "🗑️ <b>\u2022 CART CLEARED \u2022</b> 🗑️\n\n"
                "<i>Your shopping cart is now empty.</i>\n\n"
                "<i>Feel free to browse our collection again!</i> ✨",
                reply_markup=keyboard,
                parse_mode="HTML",
            )
        except Exception as edit_err:
            logger.error(f"Error updating message after clearing cart: {edit_err}")
            # Try to send a new message if editing fails
            try:
                await callback_query.message.answer(
                    "🗑️ <b>\u2022 CART CLEARED \u2022</b> 🗑️\n\n"
                    "<i>Your shopping cart is now empty.</i>\n\n"
                    "<i>Feel free to browse our collection again!</i> ✨",
                    reply_markup=keyboard,
                    parse_mode="HTML",
                )
            except Exception as send_err:
                logger.error(f"Failed to send new message after cart clear: {send_err}")

    except Exception as e:
        logger.exception(f"Error in clear_cart_handler for user {user_id}:", exc_info=e)
        # Try to show an error message to the user
        try:
            await callback_query.answer(
                "❌ Error clearing cart. Please try again later.", show_alert=True
            )
        except Exception:
            logger.error(f"Failed to show error alert to user {user_id}")

        # Try to send a fallback error message
        try:
            await callback_query.message.answer(
                "❌ <b>Error clearing cart</b>\n\nPlease try again or contact support if the issue persists.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔄 Try Again", callback_data="clear_cart"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Main Menu", callback_data="return_to_main"
                            )
                        ],
                    ]
                ),
            )
        except Exception as fallback_err:
            logger.error(f"Failed to send fallback error message: {fallback_err}")


# --- Line-Based Product Handlers ---


async def handle_line_product_add_to_cart(callback_query: types.CallbackQuery, product: dict, product_id):
    """Handle adding line-based products to cart with quantity selection."""
    from utils.line_product_manager import line_product_manager

    # Get user-specific available stock for shared inventory products
    user_id = callback_query.from_user.id
    available_lines = product.get("available_lines", 0)
    max_quantity_per_order = product.get("max_quantity_per_order", 1)

    # For shared inventory products, get user-specific availability
    if product.get("allow_shared_inventory", False):
        from database.operations import get_available_lines_for_user
        total_lines = product.get("total_lines", 0)
        user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
        available_lines = len(user_available_lines)

    if available_lines <= 0:
        await callback_query.answer("❌ This product is out of stock for you!", show_alert=True)
        return

    # Store product info in state for quantity selection
    from utils.state_helpers import safe_update_data
    from aiogram.fsm.context import FSMContext
    from states.states import OrderStates

    # Get state from callback_query
    state = FSMContext(
        storage=callback_query.bot.session.storage,
        key=callback_query.bot.session.storage.get_key(
            bot_id=callback_query.bot.id,
            chat_id=callback_query.message.chat.id,
            user_id=callback_query.from_user.id
        )
    )

    await safe_update_data(state,
        line_product_id=product_id,
        line_product_data=product,
        action_type="add_to_cart"
    )

    # Use consistent availability calculation for both text and buttons
    allow_shared_inventory = product.get("allow_shared_inventory", False)

    # Clear cache to ensure fresh data for both text and buttons
    if allow_shared_inventory:
        line_product_manager.clear_validation_cache(product_id, user_id)

    # Use cart display with pre-calculated availability for consistency
    quantity_message = line_product_manager.create_cart_quantity_display_with_availability(
        product, available_lines, allow_shared_inventory, user_id=user_id
    )

    # Use keyboard with pre-calculated availability (no internal calculation)
    keyboard = line_product_manager.create_quantity_selection_keyboard_with_availability(
        product_id, max_quantity_per_order, available_lines, allow_shared_inventory, user_id=user_id
    )

    await safe_edit_message(
        callback_query.message,
        quantity_message,
        reply_markup=keyboard,
        parse_mode="HTML"
    )

    await state.set_state(OrderStates.selecting_quantity)


@router.callback_query(F.data.startswith("select_quantity:"))
async def handle_quantity_selection(callback_query: types.CallbackQuery, state: FSMContext):
    """Handle quantity selection for line-based products."""
    await callback_query.answer()

    quantity_str = callback_query.data.split(":")[1]

    if quantity_str == "custom":
        # Handle custom quantity input
        await callback_query.message.edit_text(
            "Please enter the quantity you want (number only):",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="❌ Cancel", callback_data="back_to_product")]
            ])
        )
        await state.set_state(OrderStates.waiting_for_custom_quantity)
        return

    try:
        quantity = int(quantity_str)
    except ValueError:
        await callback_query.answer("Invalid quantity!", show_alert=True)
        return

    # Get stored product data
    data = await state.get_data()
    product_id = data.get("line_product_id")
    product_data = data.get("line_product_data")
    action_type = data.get("action_type", "add_to_cart")

    if not product_id or not product_data:
        await callback_query.answer("Error: Product data not found!", show_alert=True)
        return

    # Validate quantity with user-specific availability for shared inventory
    available_lines = product_data.get("available_lines", 0)
    max_quantity = product_data.get("max_quantity_per_order", 1)
    allow_shared_inventory = product_data.get("allow_shared_inventory", False)
    user_id = callback_query.from_user.id

    # For shared inventory, get user-specific availability
    if allow_shared_inventory:
        from database.operations import get_available_lines_for_user
        total_lines = product_data.get("total_lines", 0)
        user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
        available_lines = len(user_available_lines)

        logger.debug(f"Quantity validation: User {user_id}, Product {product_id}, "
                   f"User-specific availability: {available_lines}/{total_lines}")

    if quantity > available_lines:
        if allow_shared_inventory:
            await callback_query.answer(f"Only {available_lines} items available for you!", show_alert=True)
        else:
            await callback_query.answer(f"Only {available_lines} items available!", show_alert=True)
        return

    if quantity > max_quantity:
        await callback_query.answer(f"Maximum {max_quantity} items per order!", show_alert=True)
        return

    # Process based on action type
    if action_type == "add_to_cart":
        await add_line_product_to_cart(callback_query, product_data, product_id, quantity)
    elif action_type == "buy_now":
        await process_line_product_buy_now(callback_query, product_data, product_id, quantity, state)


@router.callback_query(F.data.startswith("line_quantity:"))
async def handle_line_quantity_selection(callback_query: types.CallbackQuery, state: FSMContext):
    """Handle line-based product quantity selection with enhanced validation."""
    await callback_query.answer()

    try:
        # Parse callback data: line_quantity:product_id:quantity
        parts = callback_query.data.split(":")
        if len(parts) != 3:
            await callback_query.answer("Invalid callback data!", show_alert=True)
            return

        product_id = parts[1]
        quantity_str = parts[2]

        try:
            quantity = int(quantity_str)
        except (ValueError, TypeError) as e:
            logger.error(f"Failed to convert quantity_str to int: {repr(quantity_str)} (type: {type(quantity_str).__name__})")
            await callback_query.answer("Invalid quantity format!", show_alert=True)
            return

        # Get stored product data or fetch from database
        data = await state.get_data()
        product_data = data.get("line_product_data")
        action_type = data.get("action_type", "add_to_cart")

        # Use the product_id from state if available, otherwise use callback data
        stored_product_id = data.get("line_product_id")
        if stored_product_id:
            product_id = stored_product_id

        # If no stored data, fetch from database
        if not product_data:
            from database.operations import get_product
            product_data = get_product(product_id)
            if not product_data:
                await callback_query.answer("Product not found!", show_alert=True)
                return

        # Validate line-based product
        if not product_data.get("is_line_based", False):
            await callback_query.answer("This is not a line-based product!", show_alert=True)
            return

        # Validate quantity with user-specific availability for shared inventory
        available_lines = product_data.get("available_lines", 0)
        max_quantity = product_data.get("max_quantity_per_order", 1)
        allow_shared_inventory = product_data.get("allow_shared_inventory", False)
        user_id = callback_query.from_user.id

        # For shared inventory, get user-specific availability
        if allow_shared_inventory:
            from database.operations import get_available_lines_for_user
            total_lines = product_data.get("total_lines", 0)
            user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
            available_lines = len(user_available_lines)

            logger.debug(f"Line quantity selection: User {user_id}, Product {product_id}, "
                       f"User-specific availability: {available_lines}/{total_lines}")

        if quantity > available_lines:
            if allow_shared_inventory:
                await callback_query.answer(f"Only {available_lines} items available for you!", show_alert=True)
            else:
                await callback_query.answer(f"Only {available_lines} items available!", show_alert=True)
            return

        if quantity > max_quantity:
            await callback_query.answer(f"Maximum {max_quantity} items per order!", show_alert=True)
            return

        # Process based on action type
        if action_type == "add_to_cart":
            await add_line_product_to_cart(callback_query, product_data, product_id, quantity)
        elif action_type == "buy_now":
            await process_line_product_buy_now(callback_query, product_data, product_id, quantity, state)
        else:
            # Default to add to cart
            await add_line_product_to_cart(callback_query, product_data, product_id, quantity)

    except ValueError:
        await callback_query.answer("Invalid quantity value!", show_alert=True)
    except Exception as e:
        logger.error(f"Error in line quantity selection: {e}")
        await callback_query.answer("An error occurred. Please try again.", show_alert=True)


@router.callback_query(F.data.startswith("line_custom_quantity:"))
async def handle_line_custom_quantity(callback_query: types.CallbackQuery, state: FSMContext):
    """Handle custom quantity input for line-based products."""
    await callback_query.answer()

    try:
        # Parse callback data: line_custom_quantity:product_id
        parts = callback_query.data.split(":")
        if len(parts) != 2:
            await callback_query.answer("Invalid callback data!", show_alert=True)
            return

        product_id = parts[1]

        # Get stored product data or fetch from database
        data = await state.get_data()
        product_data = data.get("line_product_data")

        # If no stored data, fetch from database
        if not product_data:
            from database.operations import get_product
            product_data = get_product(product_id)
            if not product_data:
                await callback_query.answer("Product not found!", show_alert=True)
                return

        # Store product info for custom quantity input
        await safe_update_data(state,
            line_product_id=product_id,
            line_product_data=product_data,
            action_type=data.get("action_type", "add_to_cart")
        )

        # Get max quantity for display with user-specific validation for shared inventory
        user_id = callback_query.from_user.id
        max_quantity_per_order = product_data.get("max_quantity_per_order", 1)
        available_lines = product_data.get("available_lines", 0)
        allow_shared_inventory = product_data.get("allow_shared_inventory", False)

        # For shared inventory, check user-specific availability
        if allow_shared_inventory:
            from database.operations import get_available_lines_for_user
            total_lines = product_data.get("total_lines", 0)
            user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
            effective_available = len(user_available_lines)
        else:
            effective_available = available_lines

        max_quantity = min(max_quantity_per_order, effective_available)

        # Check if any quantity is available
        if max_quantity <= 0:
            if allow_shared_inventory:
                error_msg = "📋 <b>No New Content Available</b>\n\nYou have already purchased all available content for this product."
            else:
                error_msg = "📋 <b>Out of Stock</b>\n\nThis product is currently out of stock."

            await callback_query.message.edit_text(
                error_msg,
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(text="🔙 Back", callback_data=f"view_product:{product_id}")]
                ]),
                parse_mode="HTML"
            )
            return

        # Show custom quantity input with appropriate messaging
        if allow_shared_inventory:
            quantity_msg = f"📋 <b>Custom Quantity Selection</b>\n\n" \
                          f"Please enter the quantity you want (1-{max_quantity}):\n\n" \
                          f"ℹ️ <i>Note: This product uses shared inventory. You can purchase up to {max_quantity} new lines.</i>"
        else:
            quantity_msg = f"📋 <b>Custom Quantity Selection</b>\n\n" \
                          f"Please enter the quantity you want (1-{max_quantity}):"

        await callback_query.message.edit_text(
            quantity_msg,
            reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="🔙 Back", callback_data=f"view_product:{product_id}")]
            ]),
            parse_mode="HTML"
        )
        await state.set_state(OrderStates.waiting_for_custom_quantity)

    except Exception as e:
        logger.error(f"Error in line custom quantity: {e}")
        await callback_query.answer("An error occurred. Please try again.", show_alert=True)


@router.message(OrderStates.waiting_for_custom_quantity)
async def handle_custom_quantity_input(message: types.Message, state: FSMContext):
    """Handle custom quantity input for line-based products."""
    try:
        quantity = int(message.text.strip())
        if quantity <= 0:
            await message.reply(
                "⚠️ <b>Invalid Quantity</b>\n\n"
                "Please enter a number greater than 0.",
                parse_mode="HTML"
            )
            return
    except ValueError:
        await message.reply(
            "⚠️ <b>Invalid Input</b>\n\n"
            "Please enter a valid number.",
            parse_mode="HTML"
        )
        return

    # Get stored product data
    data = await state.get_data()
    product_id = data.get("line_product_id")
    product_data = data.get("line_product_data")
    action_type = data.get("action_type", "add_to_cart")

    if not product_id or not product_data:
        await message.reply(
            "❌ <b>Error</b>\n\n"
            "Product data not found. Please start over.",
            parse_mode="HTML"
        )
        await clear_state_data(state)
        return

    # Use centralized validation from line product manager
    from utils.line_product_manager import line_product_manager
    user_id = message.from_user.id

    try:
        validation = await line_product_manager.validate_line_product_purchase(
            product_id, quantity, use_cache=False, user_id=user_id
        )

        if not validation["valid"]:
            await message.reply(
                f"⚠️ <b>Invalid Quantity</b>\n\n"
                f"{validation['error']}",
                parse_mode="HTML"
            )
            return

    except Exception as e:
        logger.error(f"Error validating custom quantity for product {product_id}: {e}")
        await message.reply(
            "❌ <b>Validation Error</b>\n\n"
            "Unable to validate quantity. Please try again.",
            parse_mode="HTML"
        )
        return

    # Process based on action type
    try:
        if action_type == "add_to_cart":
            await add_line_product_to_cart_from_message(message, product_data, product_id, quantity)
        elif action_type == "buy_now":
            await process_line_product_buy_now_from_message(message, product_data, product_id, quantity, state)

        # Clear state after successful processing
        await clear_state_data(state)

    except Exception as e:
        logger.error(f"Error processing custom quantity for product {product_id}: {e}")
        await message.reply(
            "❌ <b>Processing Error</b>\n\n"
            "Unable to process your request. Please try again.",
            parse_mode="HTML"
        )


async def add_line_product_to_cart(callback_query: types.CallbackQuery, product: dict, product_id, quantity: int):
    """Add line-based product with quantity to cart."""
    user_id = callback_query.from_user.id

    # Get current cart
    cart = get_or_create_cart(user_id)
    cart_items = cart.get("items", [])

    # Check if product already in cart
    existing_item = None
    for item in cart_items:
        if str(item.get("product_id")) == str(product_id):
            existing_item = item
            break

    if existing_item:
        # Update quantity with proper validation
        new_quantity = existing_item.get("quantity", 1) + quantity

        # Use centralized validation for the new total quantity
        from utils.line_product_manager import line_product_manager
        try:
            validation = await line_product_manager.validate_line_product_purchase(
                product_id, new_quantity, use_cache=False, user_id=user_id
            )

            if not validation["valid"]:
                await callback_query.answer(f"Cannot add more. {validation['error']}", show_alert=True)
                return

        except Exception as e:
            logger.error(f"Error validating cart update for product {product_id}: {e}")
            await callback_query.answer("Unable to validate quantity. Please try again.", show_alert=True)
            return

        existing_item["quantity"] = new_quantity
        # Update total price for the item
        line_price = product.get("line_price", product.get("price", 0))
        existing_item["price"] = float(line_price) * new_quantity
    else:
        # Add new item using the consolidated line product manager
        from utils.line_product_manager import line_product_manager
        new_item = await line_product_manager.create_cart_item(
            product, product_id, quantity
        )
        cart_items.append(new_item)

    # Update cart
    update_cart(user_id, cart_items)

    # Show success message
    success_message = (
        f"✅ <b>Added to Cart!</b>\n\n"
        f"<b>Product:</b> {product.get('name')}\n"
        f"<b>Quantity:</b> {quantity} items\n"
        f"<b>Total Price:</b> ${float(product.get('line_price', product.get('price', 0))) * quantity:.2f}\n\n"
        f"Continue shopping or view your cart."
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="🛒 View Cart", callback_data="view_cart")],
        [InlineKeyboardButton(text="🛍️ Continue Shopping", callback_data="browse_products")],
        [InlineKeyboardButton(text="🏠 Main Menu", callback_data="return_to_main")]
    ])

    await safe_edit_message(
        callback_query.message,
        success_message,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def _process_line_product_buy_now_shared(
    user_id: int,
    product: dict,
    product_id,
    quantity: int,
    state: FSMContext,
    context_type: str = "callback"
) -> dict:
    """
    Shared logic for processing line-based product buy now operations.

    Args:
        user_id: User ID
        product: Product dictionary
        product_id: Product ID
        quantity: Quantity to purchase
        state: FSM context
        context_type: Either "callback" or "message" for different response handling

    Returns:
        Dict with success status and response data
    """
    # Add debugging
    logger.debug(f"_process_line_product_buy_now_shared called with product_id: {repr(product_id)} (type: {type(product_id).__name__})")

    # Clear existing cart
    clear_cart(user_id)

    # Calculate total price
    line_price = product.get("line_price", product.get("price", 0))
    total_price = float(line_price) * quantity

    # Create cart item using consolidated factory
    from utils.cart_item_factory import cart_item_factory

    cart_item = cart_item_factory.create_line_based_cart_item(
        product=product,
        product_id=product_id,
        quantity=quantity
    )

    # Check balance for callback context (immediate checkout)
    if context_type == "callback":
        balance = get_user_balance(user_id)
        bypass_balance_check = should_bypass_balance_check(user_id)

        if not bypass_balance_check and balance < total_price:
            # Return insufficient balance data
            return {
                "success": False,
                "error_type": "insufficient_balance",
                "total_price": total_price,
                "balance": balance,
                "needed": total_price - balance,
                "product_id": product_id
            }

        # Proceed to confirmation for callback context
        from states.states import OrderStates
        await state.set_state(OrderStates.confirm_order)
        state_data = {
            "cart_items": [cart_item],
            "order_total": total_price,
            "user_id": user_id,
            "bypass_balance_check": bypass_balance_check,
            "checkout_time": datetime.now().isoformat(),
        }
        await state.update_data(**state_data)

        return {
            "success": True,
            "context_type": "callback",
            "confirmation_message": (
                f"🛒 <b>Confirm Your Purchase</b>\n\n"
                f"<b>Product:</b> {product.get('name')}\n"
                f"<b>Quantity:</b> {quantity} items\n"
                f"<b>Price per item:</b> ${line_price:.2f}\n"
                f"<b>Total:</b> ${total_price:.2f}\n\n"
                f"<b>Your Balance:</b> ${balance:.2f}\n"
            ),
            "balance": balance,
            "bypass_balance_check": bypass_balance_check,
            "total_price": total_price,
            "product_id": product_id
        }
    else:
        # Message context - add to cart and prepare for checkout
        cart_items = [cart_item]
        update_cart(user_id, cart_items)

        # Store purchase data in state for checkout
        await safe_update_data(state,
            cart_items=cart_items,
            total_price=total_price,
            product_id=product_id,
            quantity=quantity,
            action_type="buy_now"
        )

        return {
            "success": True,
            "context_type": "message",
            "confirmation_message": (
                f"🛒 <b>Ready to Purchase</b>\n\n"
                f"<b>Product:</b> {product.get('name')}\n"
                f"<b>Quantity:</b> {quantity} items\n"
                f"<b>Total Price:</b> ${total_price:.2f}\n\n"
                f"Proceed to checkout?"
            ),
            "product_id": product_id
        }


async def process_line_product_buy_now(callback_query: types.CallbackQuery, product: dict, product_id, quantity: int, state: FSMContext):
    """Process buy now for line-based products."""
    user_id = callback_query.from_user.id

    result = await _process_line_product_buy_now_shared(
        user_id, product, product_id, quantity, state, "callback"
    )

    if not result["success"]:
        if result["error_type"] == "insufficient_balance":
            # Insufficient balance
            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="💰 Add Funds", callback_data="deposit")],
                [InlineKeyboardButton(text="⬅️ Back", callback_data=f"select_product:{product_id}")]
            ])

            await safe_edit_message(
                callback_query.message,
                f"❌ <b>Insufficient Balance</b>\n\n"
                f"<b>Total:</b> ${result['total_price']:.2f}\n"
                f"<b>Your Balance:</b> ${result['balance']:.2f}\n"
                f"<b>Needed:</b> ${result['needed']:.2f} more",
                reply_markup=keyboard,
                parse_mode="HTML"
            )
        return

    # Show confirmation
    confirmation_message = result["confirmation_message"]

    if result["bypass_balance_check"]:
        confirmation_message += f"<i>Sandbox mode: Balance will not be deducted</i>\n\n"
    else:
        confirmation_message += f"<b>New Balance:</b> ${result['balance'] - result['total_price']:.2f}\n\n"

    confirmation_message += "Confirm your purchase to receive your items immediately."

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="✅ Confirm Purchase", callback_data="confirm_order")],
        [InlineKeyboardButton(text="❌ Cancel", callback_data=f"select_product:{product_id}")]
    ])

    await safe_edit_message(
        callback_query.message,
        confirmation_message,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# --- Purchase Flow (Checkout & Order Processing) ---


@router.callback_query(F.data.startswith("buy_product:"))
async def buy_product_now(callback_query: types.CallbackQuery, state: FSMContext):
    """Handle 'Buy Now' - Add single item to cart and proceed to checkout confirmation."""
    user_id = callback_query.from_user.id
    try:
        await callback_query.answer("Preparing quick checkout...")
        try:
            product_id_str = callback_query.data.split(":")[1]
            product_id = _get_product_id_from_string(product_id_str)
        except (IndexError, ValueError) as e:
            logger.error(f"Invalid callback data format: {callback_query.data}")
            await callback_query.answer("❌ Invalid product selection!", show_alert=True)
            return

        product = get_product(product_id)
        if not product:
            logger.warning(
                f"Buy Now failed: Product {product_id_str} not found for user {user_id}."
            )
            await callback_query.answer("❌ Product not available!", show_alert=True)
            return

        actual_product_id = product.get("_id") or product.get("id")
        if not actual_product_id:
            logger.error(f"Product {product.get('name')} has no usable ID for buy now.")
            await callback_query.answer(
                "❌ Error processing product (data issue).", show_alert=True
            )
            return

        # Check if this is an exclusive single-use product
        if product.get("is_exclusive_single_use", False):
            # Handle exclusive product purchase
            from utils.exclusive_product_db_operations import ExclusiveProductDBOperations

            # Validate exclusive product availability
            validation = ExclusiveProductDBOperations.check_user_can_purchase_exclusive(
                actual_product_id, user_id
            )

            if not validation["can_purchase"]:
                await callback_query.answer(f"❌ {validation['reason']}", show_alert=True)
                return

            # Proceed with exclusive product purchase flow
            logger.info(f"Processing exclusive product purchase for user {user_id}, product {actual_product_id}")

        # Check if this is a line-based product that needs quantity selection
        elif product.get("is_line_based", False):
            # For line-based products, use the enhanced line product manager for consistent user-specific handling
            from utils.line_product_manager import line_product_manager

            # Get basic product info
            available_lines = product.get("available_lines", 0)
            max_quantity_per_order = product.get("max_quantity_per_order", 1)
            allow_shared_inventory = product.get("allow_shared_inventory", False)

            # Calculate user-specific availability once for consistency between text and buttons
            try:
                # Single source of truth for user-specific availability
                if allow_shared_inventory:
                    from database.operations import get_available_lines_for_user
                    total_lines = product.get("total_lines", 0)

                    # Clear cache to ensure fresh data for both text and buttons
                    line_product_manager.clear_validation_cache(actual_product_id, user_id)

                    user_available_lines = get_available_lines_for_user(user_id, actual_product_id, total_lines)
                    available_lines = len(user_available_lines)

                    logger.debug(f"Express checkout: User {user_id}, Product {actual_product_id}, "
                               f"User-specific availability: {available_lines}/{total_lines}")
                else:
                    # For non-shared inventory, use the product's available lines
                    available_lines = product.get("available_lines", 0)

                if available_lines <= 0:
                    if allow_shared_inventory:
                        await callback_query.answer("❌ You have purchased all available content from this product!", show_alert=True)
                    else:
                        await callback_query.answer("❌ This product is out of stock!", show_alert=True)
                    return

                # Create product display with the calculated availability (no internal calculation)
                product_display = line_product_manager.create_express_checkout_display_with_availability(
                    product, available_lines, allow_shared_inventory, user_id=user_id
                )

                # Store product info in state for quantity selection
                await safe_update_data(state,
                    line_product_id=actual_product_id,
                    line_product_data=product,
                    action_type="buy_now"
                )

                # Create well-structured express checkout message with clear sections
                quantity_message = (
                    f"🛒 <b>Express Checkout</b>\n"
                    f"{LineProductTheme.SECTION_DIVIDER}\n\n"
                    f"{product_display}\n\n"
                    f"{LineProductTheme.SECTION_DIVIDER}\n"
                    f"<b>🔢 Select Quantity:</b> Choose how many items to purchase"
                )

                # Use keyboard with pre-calculated availability (no internal calculation)
                keyboard = line_product_manager.create_quantity_selection_keyboard_with_availability(
                    actual_product_id,
                    max_quantity_per_order,
                    available_lines,
                    allow_shared_inventory,
                    user_id=user_id
                )

                await safe_edit_message(
                    callback_query.message,
                    quantity_message,
                    reply_markup=keyboard,
                    parse_mode="HTML"
                )

            except Exception as display_e:
                logger.error(f"Error creating express checkout display for product {actual_product_id}: {display_e}")
                await callback_query.answer("❌ Error preparing checkout. Please try again.", show_alert=True)
                return

            await state.set_state(OrderStates.selecting_quantity)
            return

        # Clear existing cart for 'Buy Now'
        clear_cart(user_id)
        logger.info(f"Cleared cart for user {user_id} before Buy Now.")

        # Create cart item using consolidated factory
        from utils.cart_item_factory import cart_item_factory

        try:
            cart_item = cart_item_factory.create_cart_item(
                product=product,
                product_id=actual_product_id,
                quantity=1
            )
            logger.debug(f"Created cart item for product {actual_product_id}: {cart_item}")
        except Exception as e:
            logger.error(f"Error creating cart item for product {actual_product_id}: {e}")
            await callback_query.answer("❌ Error processing product. Please try again.", show_alert=True)
            return
        cart_items = [cart_item]

        # Calculate total from cart item price
        if not cart_item:
            logger.error(f"Cart item creation failed for product {actual_product_id}")
            await callback_query.answer("❌ Error creating cart item. Please try again.", show_alert=True)
            return

        cart_item_price = cart_item.get("price", 0)
        if cart_item_price is None:
            logger.error(f"Cart item has no price for product {actual_product_id}: {cart_item}")
            await callback_query.answer("❌ Error calculating price. Please try again.", show_alert=True)
            return

        total = float(cart_item_price)
        balance = get_user_balance(user_id)
        bypass_balance_check = should_bypass_balance_check(user_id)

        # Check balance
        if balance < total and not bypass_balance_check:
            logger.warning(
                f"Buy Now insufficient balance for user {user_id}. Needed: {total}, Has: {balance}"
            )
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="💰 Add Credit", callback_data="deposit_funds"
                        )
                    ],
                    # Link back to the specific product
                    [
                        InlineKeyboardButton(
                            text="🔙 View Product",
                            callback_data=f"select_product:{actual_product_id}",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🛍️ Browse Collection", callback_data="browse_products"
                        )
                    ],
                ]
            )
            await safe_edit_message(
                callback_query.message,
                f"❌ <b>\u2022 INSUFFICIENT BALANCE \u2022</b> ❌\n\n"
                f"<b>Item:</b> {product.get('name')}\n"
                f"<b>Price:</b> <code>${total:.2f}</code>\n"
                f"<b>Your Balance:</b> <code>${balance:.2f}</code>\n"
                f"<b>Needed:</b> <code>${total - balance:.2f}</code> more\n\n"
                f"<i>Please add funds to proceed.</i>",
                reply_markup=keyboard,
                parse_mode="HTML",
            )
            return

        # Proceed to confirmation
        await clear_state_data(state)  # Use our safer clear function
        await state.set_state(OrderStates.confirm_order)
        state_data = {
            "cart_items": cart_items,  # Store the single item
            "order_total": total,
            "user_id": user_id,
            "bypass_balance_check": bypass_balance_check,
            "checkout_time": datetime.now().isoformat(),  # Optional metadata
        }
        await safe_update_data(state, **state_data)  # Use our safer update function
        logger.info(f"Set OrderStates.confirm_order for user {user_id} (Buy Now)")

        confirmation_text = (
            f"🌟 <b>\u2022 EXPRESS CHECKOUT \u2022</b> 🌟\n\n"
            f"<b>Item:</b> {product.get('name')}\n"
            f"<b>Price:</b> <code>${total:.2f}</code>\n"
            f"<b>Your Balance:</b> <code>${balance:.2f}</code>\n"
        )
        if bypass_balance_check:
            confirmation_text += (
                f"<b>Remaining:</b> <code>${balance:.2f}</code> (Sandbox Mode)\n"
            )
        else:
            confirmation_text += (
                f"<b>Remaining After:</b> <code>${balance - total:.2f}</code>\n"
            )
        confirmation_text += "\n<i>Confirm to complete your purchase instantly.</i>"

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="✅ Confirm Purchase", callback_data="confirm_order"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="❌ Cancel", callback_data="cancel_checkout"
                    )
                ],  # Use same cancel action
            ]
        )

        await safe_edit_message(
            callback_query.message,
            confirmation_text,
            reply_markup=keyboard,
            parse_mode="HTML",
        )

    except Exception as e:
        logger.exception(
            f"Error during Buy Now for user {user_id} (Prod ID str: {callback_query.data.split(':')[1]}):",
            exc_info=e,
        )
        await callback_query.answer("❌ Error preparing checkout.", show_alert=True)
        await clear_state_data(state)  # Use our safer clear function


@router.callback_query(F.data == "checkout")
async def checkout(callback_query: types.CallbackQuery, state: FSMContext):
    """Initiate checkout for the items currently in the cart."""
    user_id = callback_query.from_user.id
    try:
        await callback_query.answer("Proceeding to checkout...")

        cart = get_or_create_cart(user_id)
        cart_items = cart.get("items", [])

        if not cart_items:
            logger.info(f"Checkout attempt with empty cart by user {user_id}.")
            await callback_query.answer("🛒 Your cart is empty!", show_alert=True)
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🛍️ Browse Products", callback_data="browse_products"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🏠 Main Menu", callback_data="return_to_main"
                        )
                    ],
                ]
            )
            await safe_edit_message(
                callback_query.message,
                "🛒 <b>\u2022 EMPTY CART \u2022</b> 🛒\n\n<i>Add some items before checking out!</i>",
                reply_markup=keyboard,
                parse_mode="HTML",
            )
            return

        # Ensure prices are floats and calculate total
        total = 0.0
        valid_cart_items = []
        for item in cart_items:
            try:
                price = float(item.get("price") or 0)
                item["price"] = price  # Ensure price is float in the item dict
                total += price
                # Only add essential fields to state? Reduces state size.
                valid_item = {
                    "product_id": item.get("product_id"),
                    "id": item.get("id") or item.get("product_id"),  # Ensure ID is available
                    "_id": item.get("_id") or item.get("product_id"),  # Ensure _id is available
                    "name": item.get("name"),
                    "price": price,
                    "file_link": item.get("file_link"),
                    "is_line_based": item.get("is_line_based", False),  # Line-based flag
                    "quantity": item.get("quantity", 1),  # Quantity for line-based products
                }
                valid_cart_items.append(valid_item)
            except (ValueError, TypeError):
                logger.error(
                    f"Invalid price for item {item.get('name')} in cart for user {user_id}. Skipping."
                )
                await callback_query.answer(
                    f"Error with item '{item.get('name')}' price. Please contact support.",
                    show_alert=True,
                )
                await clear_state_data(state)  # Use our safer clear function
                return  # Stop checkout if item is invalid

        if not valid_cart_items:
            logger.error(f"All cart items had invalid prices for user {user_id}.")
            await callback_query.answer(
                "Error processing cart items. Please contact support.", show_alert=True
            )
            await clear_state_data(state)  # Use our safer clear function
            return

        balance = get_user_balance(user_id)
        bypass_balance_check = should_bypass_balance_check(user_id)

        # Check balance
        if balance < total and not bypass_balance_check:
            logger.warning(
                f"Checkout insufficient balance for user {user_id}. Needed: {total}, Has: {balance}"
            )
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="💰 Add Credit", callback_data="deposit_funds"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Return to Cart", callback_data="view_cart"
                        )
                    ],
                ]
            )
            await safe_edit_message(
                callback_query.message,
                f"❌ <b>\u2022 INSUFFICIENT BALANCE \u2022</b> ❌\n\n"
                f"<b>Order Total:</b> <code>${total:.2f}</code>\n"
                f"<b>Your Balance:</b> <code>${balance:.2f}</code>\n"
                f"<b>Needed:</b> <code>${total - balance:.2f}</code> more\n\n"
                f"<i>Please add funds to proceed.</i>",
                reply_markup=keyboard,
                parse_mode="HTML",
            )
            return

        # Proceed to confirmation
        await clear_state_data(state)  # Use our safer clear function
        await state.set_state(OrderStates.confirm_order)
        state_data = {
            "cart_items": valid_cart_items,  # Store sanitized items
            "order_total": total,
            "user_id": user_id,
            "bypass_balance_check": bypass_balance_check,
            "checkout_time": datetime.now().isoformat(),
        }
        await safe_update_data(state, **state_data)  # Use our safer update function
        logger.info(f"Set OrderStates.confirm_order for user {user_id} (Cart Checkout)")

        # Prepare confirmation message text
        confirmation_text = "🛒 <b>\u2022 ORDER CONFIRMATION \u2022</b> 🛒\n\n"
        confirmation_text += "<b>Items:</b>\n"
        for i, item in enumerate(valid_cart_items, 1):
            confirmation_text += (
                f"{i}. {item.get('name')} - <code>${item.get('price', 0):.2f}</code>\n"
            )
        confirmation_text += f"\n<b>Total:</b> <code>${total:.2f}</code>"
        confirmation_text += f"\n<b>Your Balance:</b> <code>${balance:.2f}</code>"
        if bypass_balance_check:
            confirmation_text += (
                f"\n<b>Remaining:</b> <code>${balance:.2f}</code> (Sandbox Mode)\n"
            )
        else:
            confirmation_text += (
                f"\n<b>Remaining After:</b> <code>${balance - total:.2f}</code>\n"
            )
        confirmation_text += "\n<i>Please confirm your order details.</i>"

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="✅ Confirm Purchase", callback_data="confirm_order"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="❌ Cancel", callback_data="cancel_checkout"
                    )
                ],
            ]
        )

        await safe_edit_message(
            callback_query.message,
            confirmation_text,
            reply_markup=keyboard,
            parse_mode="HTML",
        )

    except Exception as e:
        logger.exception(
            f"Error during checkout initiation for user {user_id}:", exc_info=e
        )
        await callback_query.answer(
            "❌ Error during checkout. Please try again.", show_alert=True
        )
        await clear_state_data(state)  # Use our safer clear function


@router.callback_query(
    F.data == "cancel_checkout", StateFilter(OrderStates.confirm_order, None)
)  # Allow cancel from state or if state lost
async def cancel_checkout(callback_query: types.CallbackQuery, state: FSMContext):
    """Handle cancellation during the checkout confirmation step."""
    await clear_state_data(state)  # Use our safer clear function
    logger.info(f"Checkout cancelled by user {callback_query.from_user.id}")
    await callback_query.answer("Checkout cancelled.")

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(text="🛒 View Cart", callback_data="view_cart")
            ],  # Go back to cart
            [
                InlineKeyboardButton(
                    text="🛍️ Browse Products", callback_data="browse_products"
                )
            ],
        ]
    )
    await safe_edit_message(
        callback_query.message,
        "✅ <b>\u2022 CHECKOUT CANCELLED \u2022</b> ✅\n\n"
        "<i>Your order was not placed. Your cart remains unchanged.</i>\n\n"
        "<i>You can view your cart or continue shopping.</i> ✨",
        reply_markup=keyboard,
        parse_mode="HTML",
    )


@router.callback_query(
    F.data == "confirm_order", StateFilter(OrderStates.confirm_order)
)
async def process_order(
    callback_query: types.CallbackQuery, state: FSMContext, bot: Bot
):
    """Process the confirmed order: check balance, update DB, deliver."""
    user_id = callback_query.from_user.id
    order_data = {}  # Initialize to ensure it exists in finally block

    # Check if order is already being processed to prevent duplicates
    processing_key = f"processing_order_{user_id}"
    if hasattr(process_order, '_processing_orders'):
        if processing_key in process_order._processing_orders:
            await callback_query.answer("⏳ Order already being processed...", show_alert=True)
            return
    else:
        process_order._processing_orders = set()

    # Mark order as being processed
    process_order._processing_orders.add(processing_key)

    try:
        await callback_query.answer("Processing your order...")  # Give feedback
        order_data = await state.get_data()

        # --- Data Validation and Recovery ---
        cart_items = order_data.get("cart_items")
        order_total = order_data.get("order_total")  # Use the reliably stored total
        stored_user_id = order_data.get("user_id")
        bypass_balance_check = order_data.get("bypass_balance_check", False)

        # Basic check: user ID consistency
        if stored_user_id != user_id:
            logger.error(
                f"State user ID mismatch! State: {stored_user_id}, Current: {user_id}. Aborting order."
            )
            raise ValueError("User ID mismatch during order processing.")

        # Check if essential data is present
        if not cart_items or order_total is None or order_total <= 0:
            logger.error(
                f"Incomplete order data in state for user {user_id}: Items={bool(cart_items)}, Total={order_total}. Attempting DB recovery."
            )
            # Attempt recovery ONLY from the user's current DB cart as a last resort
            # This assumes the user didn't modify the cart between checkout and confirm
            cart = get_or_create_cart(user_id)
            db_cart_items = cart.get("items", [])
            if db_cart_items:
                logger.warning(
                    f"Recovered {len(db_cart_items)} items from DB cart for user {user_id}."
                )
                cart_items = db_cart_items  # Use DB items
                # Recalculate total from recovered items
                order_total = sum(
                    float(item.get("price", 0) or 0) for item in cart_items
                )
                if order_total <= 0:
                    logger.error(
                        f"Recalculated total from DB cart is invalid ({order_total}) for user {user_id}."
                    )
                    raise ValueError("Invalid order total after recovery.")
                logger.info(
                    f"Using recalculated total ${order_total:.2f} from DB cart."
                )
            else:
                logger.critical(
                    f"CRITICAL: Cannot process order for user {user_id}. State data missing and DB cart empty/unavailable."
                )
                raise ValueError("Could not retrieve valid order details.")

        logger.info(
            f"Processing order for user {user_id}. Items: {len(cart_items)}, Total: {order_total}, Bypass: {bypass_balance_check}"
        )

        # --- Pre-checkout Cart Validation ---
        logger.info(f"🔍 Starting cart validation for user {user_id}")

        # Use existing cart items to avoid deadlock - they're already validated
        logger.info(f"Using existing cart items for user {user_id}: {len(cart_items)} items")

        # Simple validation - check if cart is empty
        if not cart_items:
            error_message = "❌ <b>Cart is Empty</b>\n\nYour cart is empty. Please add items before checkout."

            await callback_query.answer()
            await safe_edit_message(
                callback_query.message,
                error_message,
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(text="🛒 Browse Products", callback_data="browse_products")],
                    [InlineKeyboardButton(text="🏠 Main Menu", callback_data="main_menu")]
                ]),
                parse_mode="HTML"
            )
            return

        # Cart validation passed - use existing cart items
        logger.info(f"✅ Cart validation passed for user {user_id}: {len(cart_items)} items")
        # Cart items are already available and validated - no need to re-fetch
        logger.info(f"✅ Using validated cart items for user {user_id}: {len(cart_items)} items")

        # Cart items are already validated before reaching this point
        logger.info(f"✅ Proceeding with order processing for user {user_id} with {len(cart_items)} validated items")
        logger.info(f"🚨 DEBUGGING: About to proceed to balance check - this should be line 3476 in the current file")

        # --- Balance Check ---
        balance = get_user_balance(user_id)
        if balance < order_total and not bypass_balance_check:
            logger.warning(
                f"Order process insufficient balance for user {user_id}. Needed: {order_total}, Has: {balance}"
            )
            await handle_insufficient_balance(
                callback_query, balance, order_total
            )  # Show specific message
            # Don't clear state here, let finally handle it
            return  # Stop processing

        # --- Process Purchase ---
        order_number = random.randint(100000, 999999)  # Unique order ID
        purchased_item_names = [item.get("name", "Unknown") for item in cart_items]
        new_balance = balance

        # 1. Update Balance (if not bypassed)
        if not bypass_balance_check:
            new_balance = balance - order_total
            update_user_balance(user_id, new_balance)
            logger.info(
                f"Updated balance for user {user_id}: {balance:.2f} -> {new_balance:.2f}"
            )
        else:
            logger.info(
                f"Sandbox mode: Balance for user {user_id} remains {balance:.2f}"
            )

        # 2. Create Transaction Record
        try:
            transaction = add_transaction(
                user_id=user_id,
                transaction_type="purchase",
                amount=order_total,
                items=purchased_item_names,
                order_id=order_number,
                note=f"Order #{order_number} ({len(cart_items)} items)"
                + (" [Sandbox]" if bypass_balance_check else ""),
            )
            if transaction:
                logger.info(
                    f"Transaction {transaction['_id']} recorded for order {order_number}."
                )
            else:
                logger.error(f"Failed to record transaction for order {order_number}!")
                # Consider how to handle this failure (e.g., refund?) - For now, log and continue delivery
        except Exception as tx_err:
            logger.exception(
                f"Error recording transaction for order {order_number}:",
                exc_info=tx_err,
            )
            # Continue with order processing but log the failure

        # 3. Update Product Inventory (if not bypassed)
        if not bypass_balance_check:
            for item in cart_items:
                product_id = item.get("product_id")
                is_line_based = item.get("is_line_based", False)
                is_exclusive_single_use = item.get("is_exclusive_single_use", False)
                quantity = item.get("quantity", 1)

                if product_id:
                    try:
                        if is_exclusive_single_use:
                            # Mark exclusive product as purchased before delivery
                            from utils.exclusive_product_db_operations import ExclusiveProductDBOperations

                            purchase_result = ExclusiveProductDBOperations.mark_exclusive_product_as_purchased(
                                product_id, user_id
                            )

                            if purchase_result.get("success"):
                                logger.info(
                                    f"Successfully marked exclusive product {product_id} as purchased by user {user_id}"
                                )
                            else:
                                logger.error(
                                    f"Failed to mark exclusive product {product_id} as purchased: {purchase_result.get('error')}"
                                )
                                # This is critical - if we can't mark as purchased, we should not proceed with delivery
                                raise Exception(f"Failed to mark exclusive product as purchased: {purchase_result.get('error')}")
                        elif is_line_based:
                            # For line-based products, reserve inventory then confirm the purchase
                            from database.operations import reserve_inventory_lines_for_user, confirm_line_purchase_with_history
                            from utils.line_inventory import line_inventory_manager

                            # First, reserve inventory for the purchase
                            reservation_result = reserve_inventory_lines_for_user(product_id, quantity, user_id)
                            if not reservation_result:
                                logger.warning(
                                    f"Failed to reserve {quantity} lines for product {product_id} for user {user_id}"
                                )
                                raise Exception(f"Failed to reserve inventory for line-based product {product_id}")

                            logger.info(f"Reserved {quantity} lines for product {product_id} for user {user_id}")

                            # For shared inventory products, we need to get the line indices that will be delivered
                            line_indices = None
                            allow_shared_inventory = product.get("allow_shared_inventory", False)
                            if allow_shared_inventory:
                                # Get the inventory file path
                                inventory_file = product.get("inventory_file_path")
                                if inventory_file:
                                    # Extract lines to get the indices (this doesn't modify the file for shared inventory)
                                    extracted_lines, extraction_success, selected_indices = line_inventory_manager.extract_lines_for_user(
                                        inventory_file, quantity, user_id, product_id, allow_shared_inventory
                                    )
                                    if extraction_success:
                                        line_indices = selected_indices
                                        logger.info(f"Selected line indices {line_indices} for user {user_id} on shared inventory product {product_id}")
                                    else:
                                        logger.error(f"Failed to extract lines for shared inventory confirmation")
                                        raise Exception(f"Failed to extract lines for shared inventory product {product_id}")

                            # Then, confirm the purchase - this converts reservations to actual purchases
                            result = confirm_line_purchase_with_history(product_id, quantity, user_id, line_indices)
                            if result:
                                logger.info(
                                    f"Confirmed purchase of {quantity} lines for product {product_id} for user {user_id}"
                                )
                            else:
                                logger.warning(
                                    f"Failed to confirm purchase of {quantity} lines for product {product_id} for user {user_id}"
                                )
                                # This is critical - if we can't confirm the purchase, we should not proceed with delivery
                                raise Exception(f"Failed to confirm purchase for line-based product {product_id}")
                        else:
                            # For regular products, no additional tracking needed
                            logger.debug(f"Regular product {product_id} purchase completed")
                    except Exception as e:
                        logger.exception(
                            f"Error updating product {product_id} (line_based: {is_line_based}, exclusive: {is_exclusive_single_use}):",
                            exc_info=e,
                        )

                        # If line-based product reservation failed, try to release any previously reserved lines
                        if is_line_based:
                            try:
                                from database.operations import release_reserved_lines
                                # Release lines for this specific product
                                release_reserved_lines(product_id, quantity, user_id)
                                logger.info(f"Released reserved lines for failed product {product_id}")
                            except Exception as release_error:
                                logger.error(f"Failed to release reserved lines for product {product_id}: {release_error}")

                        # Re-raise for exclusive products and line-based products as this is critical
                        if is_exclusive_single_use or is_line_based:
                            raise

        # 4. Clear Cart (DB)
        clear_cart(user_id)
        logger.info(
            f"Cleared cart for user {user_id} after order {order_number} completion."
        )

        # 5. Bonus application removed - bonuses are now applied during deposits

        # 6. Log Purchase (External/Channel Log)
        try:
            await log_purchase(
                bot,
                user_id,
                order_number,
                purchased_item_names,
                order_total,
                new_balance,
            )
        except Exception as log_e:
            logger.error(
                f"Failed to log purchase to channel for order {order_number}: {log_e}"
            )

        # 6.5. Cache invalidation is now handled by the delivery process after purchase confirmation
        # This ensures that caches are cleared only after the actual purchase is confirmed and lines are extracted
        logger.debug(f"Cache invalidation will be handled by delivery process for order {order_number}")

        # 7. Send Success Message & Deliver Files/Content
        logger.info(f"About to call send_success_message for order {order_number}, user {user_id}")
        await send_success_message(
            callback_query,
            bot,
            cart_items,
            order_number,
            order_total,
            new_balance,
            bypass_balance_check,
        )
        logger.info(f"Completed send_success_message for order {order_number}, user {user_id}")

        # 7. Notify Admin - ONLY for real purchases, not sandbox mode
        if ADMIN_ID and not bypass_balance_check:
            await notify_admin_about_order(
                bot,
                ADMIN_ID,
                cart_items,
                order_number,
                order_total,
                user_id,
                callback_query.from_user.full_name,
                bypass_balance_check,
                new_balance,
            )
            logger.info(f"Admin notified about order {order_number}")
        elif bypass_balance_check:
            logger.info(f"Admin notification skipped for sandbox order {order_number}")

        logger.info(f"Order {order_number} processed successfully for user {user_id}.")

    except Exception as e:
        logger.exception(
            f"CRITICAL Error processing order for user {user_id}:", exc_info=e
        )

        # Try to release any reserved lines for line-based products in the failed order
        try:
            for item in cart_items:
                if item.get("is_line_based", False):
                    product_id = item.get("product_id")
                    quantity = item.get("quantity", 1)
                    if product_id:
                        from database.operations import release_reserved_lines
                        release_reserved_lines(product_id, quantity, user_id)
                        logger.info(f"Released {quantity} reserved lines for product {product_id} due to order failure")
        except Exception as release_error:
            logger.error(f"Failed to release reserved lines after order failure: {release_error}")

        await handle_order_error(callback_query, bot, user_id, e)
        # NOTE: Balance might have been deducted but transaction/delivery failed.
        # Manual intervention might be required based on logs.

    finally:
        # ALWAYS clear state after processing attempt (success or failure)
        await state.clear()

        # Clear processing flag
        processing_key = f"processing_order_{user_id}"
        if hasattr(process_order, '_processing_orders'):
            process_order._processing_orders.discard(processing_key)

        logger.debug(
            f"Cleared state and processing flag for user {user_id} after order processing attempt."
        )


async def handle_insufficient_balance(
    callback_query: types.CallbackQuery, balance: float, total: float
):
    """Inform user about insufficient balance during purchase confirmation."""
    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [InlineKeyboardButton(text="💰 Add Credit", callback_data="deposit_funds")],
            [
                InlineKeyboardButton(text="🛒 View Cart", callback_data="view_cart")
            ],  # User might want to adjust cart
            [InlineKeyboardButton(text="🏠 Main Menu", callback_data="return_to_main")],
        ]
    )
    await safe_edit_message(
        callback_query.message,
        f"❌ <b>\u2022 PAYMENT FAILED \u2022</b> ❌\n\n"
        f"<b>Reason:</b> Insufficient Balance\n"
        f"<b>Needed:</b> <code>${total:.2f}</code>\n"
        f"<b>Available:</b> <code>${balance:.2f}</code>\n"
        f"<b>Shortfall:</b> <code>${total - balance:.2f}</code>\n\n"
        f"<i>Your order could not be completed. Please add funds or modify your cart.</i>",
        reply_markup=keyboard,
        parse_mode="HTML",
    )


async def handle_order_error(
    callback_query: types.CallbackQuery, bot: Bot, user_id: int, exception: Exception
):
    """Handle unexpected errors during order processing, notify user and admin."""
    error_message = str(exception)
    logger.critical(
        f"Order processing failed for user {user_id}. Error: {error_message}",
        exc_info=True,
    )

    # Notify Admin
    if ADMIN_ID:
        try:
            await bot.send_message(
                ADMIN_ID,
                f"⚠️ <b>ORDER PROCESSING ERROR</b> ⚠️\n\n"
                f"User: {callback_query.from_user.full_name} (<code>{user_id}</code>)\n"
                f"Error: <pre>{error_message}</pre>\n\n"
                f"<i>Check logs. Manual intervention/refund might be needed!</i>",
                parse_mode="HTML",
            )
        except Exception as admin_notify_e:
            logger.error(
                f"Failed to notify admin about order processing error: {admin_notify_e}"
            )

    # Notify User
    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            # Don't offer retry - error is likely persistent
            [
                InlineKeyboardButton(text="🛒 View Cart", callback_data="view_cart")
            ],  # Cart might be cleared, but worth showing
            [
                InlineKeyboardButton(
                    text="🛟 Contact Support", callback_data="contact_support"
                )
            ],  # Define this callback
            [InlineKeyboardButton(text="🏠 Main Menu", callback_data="return_to_main")],
        ]
    )
    await safe_edit_message(
        callback_query.message,
        "❌ <b>\u2022 ORDER ERROR \u2022</b> ❌\n\n"
        "<i>We encountered a problem while finalizing your order.</i>\n"
        "<i>Your payment may or may not have been processed.</i>\n\n"
        "<b>Please contact support for assistance.</b>",
        reply_markup=keyboard,
        parse_mode="HTML",
    )


async def send_success_message(
    callback_query: types.CallbackQuery,
    bot: Bot,  # Pass bot explicitly
    cart_items: list,
    order_number: int,
    total: float,
    new_balance: float,
    bypass_balance_check: bool = False,
):
    """Send the purchase success message and deliver associated files/text."""
    user_id = callback_query.from_user.id
    chat_id = callback_query.message.chat.id  # Use chat_id from original message
    # Note: user_name reserved for future personalization features

    logger.info(f"🚀 SEND_SUCCESS_MESSAGE STARTED for order {order_number}, user {user_id}, items: {len(cart_items)}")

    # Use themed delivery confirmation message
    from utils.line_product_manager import LineProductTheme
    delivery_summary = LineProductTheme.create_header("PURCHASE SUCCESSFUL", "ORDER COMPLETED")
    delivery_summary += f"{LineProductTheme.EMOJIS['success']} <b>Your order has been delivered successfully!</b>\n\n"

    delivery_summary += f"🧾 <b>Order:</b> <code>#{order_number}</code>\n"
    delivery_summary += f"{LineProductTheme.format_price(total, 'Total Paid')}\n"

    # Bonus information removed - bonuses are now applied during deposits

    if bypass_balance_check:
        delivery_summary += f"💎 <b>Balance:</b> <code>${new_balance:.2f}</code> <i>(Sandbox Mode)</i>\n"
    else:
        delivery_summary += f"💎 <b>New Balance:</b> <code>${new_balance:.2f}</code>\n"

    delivery_summary += f"\n{LineProductTheme.HEADER_DIVIDER}\n"
    delivery_summary += f"{LineProductTheme.EMOJIS['delivery']} <b>Your Items:</b>\n"
    delivery_summary += f"{LineProductTheme.HEADER_DIVIDER}\n"
    delivery_details = []  # To store messages for file/text delivery

    # Import the function to get file paths
    from utils.image_handler import get_product_file_path
    from utils.digital_delivery import get_delivery_manager

    # Use consolidated delivery for all items
    delivery_results = []

    for i, item in enumerate(cart_items, 1):
        item_name = item.get("name", "Product")
        item_price = float(item.get("price", 0))
        item_quantity = item.get("quantity", 1)  # Get quantity for line-based products
        file_link = item.get("file_link", "")  # Could be URL, file_id:, text:, or empty
        file_path = item.get("file_path", "")  # Local file path if stored
        is_line_based = item.get("is_line_based", False)  # Check if line-based product

        # Display item with quantity if more than 1
        if item_quantity > 1:
            delivery_summary += (
                f"<b>{i}. {item_name}</b> (x{item_quantity}) (<code>${item_price:.2f}</code>)\n"
            )
        else:
            delivery_summary += (
                f"<b>{i}. {item_name}</b> (<code>${item_price:.2f}</code>)\n"
            )

        # Use consolidated delivery for all product types
        delivery_manager = get_delivery_manager()
        logger.info(f"Delivery manager status: {'Available' if delivery_manager else 'None'}")
        if delivery_manager:
            try:
                logger.info(f"Starting delivery for order {order_number}, item: {item_name}, line_based: {is_line_based}")

                # Use the consolidated delivery method
                delivery_result = await delivery_manager.deliver_product_consolidated(
                    user_id=user_id,
                    cart_item=item,
                    order_number=order_number
                )

                delivery_results.append(delivery_result)
                logger.info(f"Delivery result for order {order_number}: {delivery_result}")

                if delivery_result.get("success"):
                    delivery_type = delivery_result.get("delivery_type", "unknown")
                    if delivery_type == "file":
                        # This covers both line-based and regular file deliveries
                        if is_line_based:
                            access_info = f"📋 <i>{item_quantity} item(s) delivered in separate file</i>"
                        else:
                            access_info = "📎 <i>File delivered in separate message</i>"
                    elif delivery_type == "exclusive":
                        access_info = "💎 <i>Exclusive file delivered in separate message</i>"
                    elif delivery_type == "link":
                        access_info = "🔗 <i>Download link provided</i>"
                    elif delivery_type == "message":
                        access_info = "💬 <i>Content delivered via direct message</i>"
                    else:
                        access_info = "✅ <i>Delivered successfully</i>"
                else:
                    error_msg = delivery_result.get("error", "Unknown error")
                    logger.error(f"Consolidated delivery failed for order {order_number}: {error_msg}")
                    access_info = "❌ <i>Delivery failed - contact support</i>"

            except Exception as e:
                logger.error(f"Error in consolidated delivery for order {order_number}: {e}")
                logger.exception(f"Full delivery error details for order {order_number}:")
                access_info = "❌ <i>Delivery error - contact support</i>"
        else:
            logger.error("Digital delivery manager not initialized - attempting to reinitialize")
            # Try to reinitialize the delivery manager
            try:
                from utils.digital_delivery import initialize_delivery_manager
                initialize_delivery_manager(bot)
                delivery_manager = get_delivery_manager()
                if delivery_manager:
                    logger.info("Successfully reinitialized delivery manager")
                    # Retry delivery with reinitialized manager
                    delivery_result = await delivery_manager.deliver_product_consolidated(
                        user_id=user_id,
                        cart_item=item,
                        order_number=order_number
                    )
                    if delivery_result.get("success"):
                        access_info = "✅ <i>Delivered successfully (after reinit)</i>"
                    else:
                        access_info = "❌ <i>Delivery failed after reinit - contact support</i>"
                else:
                    logger.error("Failed to reinitialize delivery manager")
                    access_info = "❌ <i>Delivery system unavailable - contact support</i>"
            except Exception as reinit_e:
                logger.error(f"Failed to reinitialize delivery manager: {reinit_e}")
                access_info = "❌ <i>Delivery system unavailable - contact support</i>"

        # Note: All delivery is now handled by the consolidated delivery method above
        # The access_info is already set based on the delivery result

        # Fallback for any items that weren't handled by consolidated delivery
        if not access_info:
            if file_link:
                if file_link.startswith(("http://", "https://")):
                    access_info = f"🔗 <a href='{file_link}'>Download Link</a>"
                else:
                    access_info = "📦 <i>Product delivered</i>"
            else:
                access_info = "✅ <i>Product delivered successfully</i>"

        delivery_summary += f"   {access_info}\n"
        if i < len(
            cart_items
        ):  # Add separator between items except after the last item
            delivery_summary += "┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\n"

    delivery_summary += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
    delivery_summary += "<i>Thank you for your purchase! Your items are ready.</i> 🌟\n"
    delivery_summary += (
        "<i>If you need any assistance, our support team is here to help!</i> ✨"
    )

    # Send the main success/summary message (edit the original confirmation message)
    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🛍️ Continue Shopping", callback_data="browse_products"
                )
            ],
            [InlineKeyboardButton(text="🏠 Main Menu", callback_data="return_to_main")],
        ]
    )
    await safe_edit_message(
        callback_query.message,
        delivery_summary,
        reply_markup=keyboard,
        parse_mode="HTML",
        # disable_web_page_preview=True,  # Good practice for messages with many links
    )

    # Final cache invalidation after all deliveries are complete to ensure fresh stock counts
    try:
        from utils.line_product_manager import line_product_manager

        for item in cart_items:
            product_id = item.get("product_id")
            is_line_based = item.get("is_line_based", False)
            allow_shared_inventory = item.get("allow_shared_inventory", False)

            # Final cache invalidation for line-based products to ensure immediate stock updates
            if is_line_based and product_id:
                if allow_shared_inventory:
                    # Clear user-specific cache to ensure fresh stock counts for this user
                    line_product_manager.clear_validation_cache(product_id, user_id)
                    logger.debug(f"Final cache invalidation for shared inventory product {product_id} after delivery to user {user_id}")
                else:
                    # Clear all caches for exclusive inventory
                    line_product_manager.invalidate_all_caches_for_product(product_id)
                    logger.debug(f"Final cache invalidation for exclusive inventory product {product_id} after delivery")
    except Exception as final_cache_e:
        logger.warning(f"Failed final cache invalidation after delivery: {final_cache_e}")
        # Don't fail the delivery for cache issues

    # Note: File delivery is now handled by the consolidated delivery system
    # Cache invalidation ensures users see updated stock counts immediately


async def notify_admin_about_order(
    bot: Bot,
    admin_id: int,
    cart_items: list,
    order_number: int,
    total: float,
    user_id: int,
    user_name: str,
    bypass_balance_check: bool = False,
    user_balance: float = None,  # Add parameter for user's remaining balance
):
    """Notify the admin about a new order via Telegram message."""
    try:
        admin_msg = f"✅ <b>New Order Received: #{order_number}</b> ✅\n\n"
        admin_msg += f"<b>Customer:</b> {user_name} (ID: <code>{user_id}</code>)\n"
        admin_msg += f"<b>Total Amount:</b> <code>${total:.2f}</code>\n"
        if user_balance is not None:
            admin_msg += f"<b>Customer Balance:</b> <code>${user_balance:.2f}</code>\n"
        if bypass_balance_check:
            admin_msg += "<b>Mode:</b> Sandbox (Balance Not Deducted)\n"
        admin_msg += "\n<b>Items Purchased:</b>\n"
        admin_msg += "<b>━━━━━━━━━━━━━━━━━━</b>\n"

        for i, item in enumerate(cart_items, 1):
            item_name = item.get("name", "N/A")
            item_price = float(item.get("price", 0))
            admin_msg += f"{i}. {item_name} (<code>${item_price:.2f}</code>)\n"

        admin_msg += "<b>━━━━━━━━━━━━━━━━━━</b>"

        await bot.send_message(admin_id, admin_msg, parse_mode="HTML")
        logger.info(f"Admin {admin_id} notified about order #{order_number}")
    except Exception as e:
        logger.exception(
            f"Failed to notify admin ({admin_id}) about order #{order_number}:",
            exc_info=e,
        )


# --- Registration ---


async def add_line_product_to_cart_from_message(message: types.Message, product: dict, product_id, quantity: int):
    """Add line-based product with quantity to cart from a message context."""
    user_id = message.from_user.id

    # Get current cart
    cart = get_or_create_cart(user_id)
    cart_items = cart.get("items", [])

    # Check if product already in cart
    existing_item = None
    for item in cart_items:
        if str(item.get("product_id")) == str(product_id):
            existing_item = item
            break

    if existing_item:
        # Update quantity with proper validation
        new_quantity = existing_item.get("quantity", 1) + quantity

        # Use centralized validation for the new total quantity
        from utils.line_product_manager import line_product_manager
        try:
            validation = await line_product_manager.validate_line_product_purchase(
                product_id, new_quantity, use_cache=False, user_id=user_id
            )

            if not validation["valid"]:
                await message.reply(f"Cannot add more. {validation['error']}", parse_mode="HTML")
                return

        except Exception as e:
            logger.error(f"Error validating cart update for product {product_id}: {e}")
            await message.reply("Unable to validate quantity. Please try again.", parse_mode="HTML")
            return

        existing_item["quantity"] = new_quantity
        # Update total price for the item
        line_price = product.get("line_price", product.get("price", 0))
        existing_item["price"] = float(line_price) * new_quantity
    else:
        # Add new item using the consolidated line product manager
        from utils.line_product_manager import line_product_manager
        new_item = await line_product_manager.create_cart_item(
            product, product_id, quantity
        )
        cart_items.append(new_item)

    # Update cart
    update_cart(user_id, cart_items)

    # Show success message
    success_message = (
        f"✅ <b>Added to Cart!</b>\n\n"
        f"<b>Product:</b> {product.get('name')}\n"
        f"<b>Quantity:</b> {quantity} items\n"
        f"<b>Total Price:</b> ${float(product.get('line_price', product.get('price', 0))) * quantity:.2f}\n\n"
        f"Continue shopping or view your cart."
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="🛒 View Cart", callback_data="view_cart")],
        [InlineKeyboardButton(text="🛍️ Continue Shopping", callback_data="browse_products")],
        [InlineKeyboardButton(text="🏠 Main Menu", callback_data="return_to_main")]
    ])

    await message.reply(
        success_message,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def process_line_product_buy_now_from_message(message: types.Message, product: dict, product_id, quantity: int, state: FSMContext):
    """Process buy now for line-based products from a message context."""
    user_id = message.from_user.id

    result = await _process_line_product_buy_now_shared(
        user_id, product, product_id, quantity, state, "message"
    )

    if result["success"]:
        # Show purchase confirmation
        confirmation_message = result["confirmation_message"]

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="💳 Proceed to Checkout", callback_data="proceed_to_checkout")],
            [InlineKeyboardButton(text="🔙 Back to Product", callback_data=f"view_product:{product_id}")],
            [InlineKeyboardButton(text="🏠 Main Menu", callback_data="return_to_main")]
        ])

        await message.reply(
            confirmation_message,
            reply_markup=keyboard,
            parse_mode="HTML"
        )


def register_product_handlers(dp: Dispatcher):
    """Register all product and purchase flow handlers with the dispatcher."""
    dp.include_router(router)
    logger.info("Product and purchase handlers registered.")
